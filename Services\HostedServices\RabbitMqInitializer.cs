﻿
using RabbitMQ.Client;

namespace eExamSmart_BE.Services.HostedServices
{
    public class RabbitMqInitializer : IHostedService
    {
        private readonly IConnectionFactory _factory;

        public RabbitMqInitializer(IConnectionFactory factory)
        {
            _factory = factory;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            // 1. Tạo kết nối và channel
            await using var connection = await _factory.CreateConnectionAsync(cancellationToken);
            await using var channel = await connection.CreateChannelAsync(cancellationToken: cancellationToken);

            // 2. Declare main exchange + main queue
            await channel.ExchangeDeclareAsync(
                exchange: "main_exchange",
                type: ExchangeType.Direct,
                durable: true,
                autoDelete: false,
                arguments: null,
                cancellationToken: cancellationToken
            );
            await channel.QueueDeclareAsync(
                queue: "grading_queue",
                durable: true,
                exclusive: false,
                autoDelete: false,
                arguments: new Dictionary<string, object?>
                {
                    ["x-dead-letter-exchange"] = "retry_exchange",
                    ["x-dead-letter-routing-key"] = "grading_queue"
                },
                cancellationToken: cancellationToken
            );
            await channel.QueueBindAsync(
                queue: "grading_queue",
                exchange: "main_exchange",
                routingKey: "grading_queue",
                cancellationToken: cancellationToken
            );

            // 3. Declare retry exchange + retry queue (delay via TTL)
            await channel.ExchangeDeclareAsync(
                exchange: "retry_exchange",
                type: ExchangeType.Direct,
                durable: true,
                autoDelete: false,
                arguments: null,
                cancellationToken: cancellationToken
            );
            await channel.QueueDeclareAsync(
                queue: "grading_retry_queue",
                durable: true,
                exclusive: false,
                autoDelete: false,
                arguments: new Dictionary<string, object?>
                {
                    ["x-dead-letter-exchange"] = "main_exchange",
                    ["x-dead-letter-routing-key"] = "grading_queue",
                    ["x-message-ttl"] = 5000    // delay 5s
                },
                cancellationToken: cancellationToken
            );
            await channel.QueueBindAsync(
                queue: "grading_retry_queue",
                exchange: "retry_exchange",
                routingKey: "grading_queue",
                cancellationToken: cancellationToken
            );

            // 4. Declare dead-letter exchange + DLQ
            await channel.ExchangeDeclareAsync(
                exchange: "dlx_exchange",
                type: ExchangeType.Direct,
                durable: true,
                autoDelete: false,
                arguments: null,
                cancellationToken: cancellationToken
            );
            await channel.QueueDeclareAsync(
                queue: "grading_dlq_queue",
                durable: true,
                exclusive: false,
                autoDelete: false,
                arguments: null,
                cancellationToken: cancellationToken
            );
            await channel.QueueBindAsync(
                queue: "grading_dlq_queue",
                exchange: "dlx_exchange",
                routingKey: "grading_queue",
                cancellationToken: cancellationToken
            );

            // 5. Đóng channel & connection sau khi khai báo xong
            await channel.CloseAsync(cancellationToken: cancellationToken);
            await connection.CloseAsync(cancellationToken: cancellationToken);
        }

        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    }
}
