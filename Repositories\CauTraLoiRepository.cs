﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface ICauTraLoiRepository : IRepository<CauTraLoi>
    {

    }
    public class CauTraLoiRepository : Repository<CauTraLoi>, ICauTraLoiRepository
    {
        public CauTraLoiRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
