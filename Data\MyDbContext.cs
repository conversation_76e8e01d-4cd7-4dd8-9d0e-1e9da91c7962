﻿using eExamSmart_BE.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace eExamSmart_BE.Data
{
    public class MyDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        public MyDbContext(DbContextOptions<MyDbContext> options, IHttpContextAccessor httpContextAccessor) : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public DbSet<BaiThi> BaiThis { get; set; } = null!;
        public DbSet<CauHoi> CauHois { get; set; } = null!;
        public DbSet<CauHoiPhanThiBaiThi> CauHoiPhanThiBaiThis { get; set; } = null!;
        public DbSet<CauTraLoi> CauTraLois { get; set; } = null!;
        public DbSet<DeThi> DeThis { get; set; } = null!;
        public DbSet<LichThi> LichThis { get; set; } = null!;
        public DbSet<LuaChon> LuaChons { get; set; } = null!;
        public DbSet<PhanCauHoi> PhanCauHois { get; set; } = null!;
        public DbSet<PhanThi> PhanThis { get; set; } = null!;
        public DbSet<PhanThiBaiThi> PhanThiBaiThis { get; set; } = null!;
        public DbSet<RandomRule> RandomRules { get; set; } = null!;
        public DbSet<RandomRuleTag> RandomRuleTags { get; set; } = null!;
        public DbSet<Tag> Tags { get; set; } = null!;
        public DbSet<CauHoiTag> CauHoiTags { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            //Loại bỏ quan hệ vòng
            foreach (var relationship in builder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
            {
                relationship.DeleteBehavior = DeleteBehavior.Restrict;
            }
        }

        public override int SaveChanges()
        {
            UpdateAuditFields();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateAuditFields();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateAuditFields()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is Auditable && (e.State == EntityState.Added || e.State == EntityState.Modified || e.State == EntityState.Deleted));

            var currentUserId = GetCurrentUserId();
            var now = DateTime.Now;

            foreach (var entry in entries)
            {
                var auditableEntity = (Auditable)entry.Entity;
                switch (entry.State)
                {
                    case EntityState.Added:
                        auditableEntity.CreatedDate = now;
                        auditableEntity.CreatedBy = currentUserId;
                        break;

                    case EntityState.Modified:
                        auditableEntity.UpdatedDate = now;
                        auditableEntity.UpdatedBy = currentUserId;
                        break;

                    case EntityState.Deleted:
                        entry.State = EntityState.Modified;
                        auditableEntity.IsDeleted = true;
                        auditableEntity.DeletedDate = now;
                        auditableEntity.DeletedBy = currentUserId;

                        auditableEntity.UpdatedDate = now;
                        auditableEntity.UpdatedBy = currentUserId;
                        break;
                }
            }
        }

        private Guid GetCurrentUserId()
        {
            // Ví dụ: lấy user id từ HttpContext nếu người dùng đã xác thực
            var userIdString = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Name)?.Value;
            return Guid.TryParse(userIdString, out var userId) ? userId : Guid.Empty;
        }
    }
}
