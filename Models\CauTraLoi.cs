﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace eExamSmart_BE.Models
{
    public class CauTraLoi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public Guid CauHoiPhanThiBaiThiId { get; set; }
        [ForeignKey(nameof(CauHoiPhanThiBaiThiId))]
        public CauHoiPhanThiBaiThi? CauHoiPhanThiBaiThi { get; set; }

        public string? SelectedLuaChonIds { get; set; }

        public string? ShortAnswerText { get; set; }

        /// <summary>
        /// Dành cho các loại câu hỏi có lựa chọn kiểu true/false
        /// Dữ liệu lưu dưới dạng JSON: { luaChonId1: true, luaChonId2: false }
        /// </summary>
        public string? BooleanAnswersJson { get; set; }

        [MaxLength(500)]
        public string? AudioUrl { get; set; }
        public string? AudioText { get; set; }
        public string? OrderingAnswer { get; set; }

        public bool? IsCorrect { get; set; }

        public float? Diem { get; set; }

        public DateTime ThoiGian { get; set; }
    }
}
