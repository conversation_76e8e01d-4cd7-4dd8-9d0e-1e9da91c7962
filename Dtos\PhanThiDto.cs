﻿namespace eExamSmart_BE.Dtos
{
    public class PhanThiDto
    {
        public Guid Id { get; set; }
        public string Ten<PERSON>hanThi { get; set; }
        public int ThuTu { get; set; }
        public string? KyNang { get; set; }
        public bool IsRandom { get; set; }
        public List<CauHoiDto> CauHoi { get; set; } = new List<CauHoiDto>();
        public RandomRuleDto RandomRule { get; set; } = new RandomRuleDto();

    }
}
