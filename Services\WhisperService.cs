﻿using System.Diagnostics;
using System.Threading.Tasks;

namespace eExamSmart_BE.Services
{
    public class WhisperService
    {
        private readonly string _whisperExecutablePath;
        private readonly string _modelsPath;

        public WhisperService(string whisperExecutablePath, string modelsPath)
        {
            _whisperExecutablePath = whisperExecutablePath;
            _modelsPath = modelsPath;
        }

        public async Task<string> TranscribeAsync(string audioFilePath)
        {
            if (!File.Exists(audioFilePath))
                throw new FileNotFoundException("File audio không tồn tại.", audioFilePath);

            // Giả sử cú pháp: whisper.exe -m <model_path> -f <audioFilePath> -otxt
            var psi = new ProcessStartInfo
            {
                FileName = _whisperExecutablePath,
                Arguments = $"-m \"{Path.Combine(_modelsPath, "ggml-base.en.bin")}\" -f \"{audioFilePath}\" -otxt",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = new Process { StartInfo = psi };
            process.Start();
            string output = await process.StandardOutput.ReadToEndAsync();
            string error = await process.StandardError.ReadToEndAsync();
            process.WaitForExit();

            if (process.ExitCode != 0)
                throw new Exception($"Whisper process lỗi: {error}");

            return output.Trim();
        }
    }
}
