﻿using eExamSmart_BE.Repositories;

namespace eExamSmart_BE.Infrastructure
{
    public interface IUnitOfWork : IDisposable
    {
        IDeThiRepository DeThis { get; }
        IPhanThiRepository PhanThis { get; }
        IPhanCauHoiRepository PhanCauHois { get; }
        IRandomRuleRepository RandomRules { get; }
        ICauHoiRepository CauHois { get; }
        ILuaChonRepository LuaChons { get; }
        ITagRepository Tags { get; }
        ILichThiRepository LichThis { get; }
        IBaiThiRepository BaiThis { get; }
        IPhanThiBaiThiRepository PhanThiBaiThis { get; }
        ICauHoiPhanThiBaiThiRepository CauHoiPhanThiBaiThis { get; }
        ICauTraLoiRepository CauTraLois { get; }
        ICauHoiTagRepository CauHoiTags { get; }
        IRandomRuleTagRepository RandomRuleTags { get; }

        /// <summary>
        /// <PERSON><PERSON><PERSON> tất cả thay đổi và trả về số bản ghi bị ảnh hưởng.
        /// </summary>
        Task<int> CompleteAsync(CancellationToken cancellationToken = default);
    }
}
