﻿using System.ComponentModel.DataAnnotations;

namespace eExamSmart_BE.Dtos
{
    public class CreateCauHoiRequest
    {
        public Guid? Id { get; set; }
        [Required]
        public string NoiDung { get; set; } = string.Empty;
        [Required]
        public string LoaiCauHoi { get; set; } = string.Empty;

        public string? MucDo { get; set; }

        public string? HinhAnhUrl { get; set; }

        public string? AmThanhUrl { get; set; }
        public string? DapAnMau { get; set; }

        public List<LuaChonDto>? LuaChons { get; set; } = new();
        public List<string>? TagIds { get; set; } = new();

    }
}
