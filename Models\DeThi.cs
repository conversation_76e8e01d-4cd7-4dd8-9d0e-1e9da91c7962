﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using eExamSmart_BE.Models.Enums;

namespace eExamSmart_BE.Models
{
    public class DeThi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        [MaxLength(50)]
        public string MaDeThi { get; set; }

        [MaxLength(200)]
        public string TenDeThi { get; set; }

        [MaxLength(1000)]
        public string? MoTa { get; set; }

        public int Duration { get; set; }
        // Thời gian thi được lưu ngay trong DeThi
        public DateTime ThoiGianBatDau { get; set; }

        public DateTime ThoiGianKetThuc { get; set; }

        public DeThiStatus Status { get; set; }

        // Navigation Properties
        public ICollection<PhanThi> PhanThis { get; set; } = new List<PhanThi>();
        public ICollection<LichThi> LichThis { get; set; } = new List<LichThi>();
        public ICollection<BaiThi> BaiThis { get; set; } = new List<BaiThi>();
    }



}
