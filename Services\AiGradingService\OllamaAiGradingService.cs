﻿using eExamSmart_BE.Services.Interfaces;
using System.Text.Json;

namespace eExamSmart_BE.Services.IAIGradingSevice
{
    public class OllamaAiGradingService : IAiGradingService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private string _baseUrl;

        public OllamaAiGradingService(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _baseUrl = _configuration["AI:Ollama:ApiUrl"]?.TrimEnd('/') ?? "http://localhost:11434";
        }
        public async Task<GradingResult> GradeShortAnswerAsync(string questionText, string correctAnswer, string userAnswer, CancellationToken ct = default)
        {
            var prompt = $@"
                Bạn là một giáo viên tiếng Anh chuyên nghiệp. Tr<PERSON><PERSON><PERSON> khi chấm, kiểm tra xem “Trả lời của thí sinh” có phải bằng tiếng Anh hay không. <PERSON><PERSON><PERSON> không, tr<PERSON> về 0 ngay lập tức mà không chấm tiếp. Sau đó, dựa trên rubric sau, chấm điểm câu trả lời: 0: Không trả lời hoặc trả lời không liên quan, không trả lời bằng tiếng anh
                0: Không trả lời hoặc trả lời không liên quan, hoặc không phải tiếng Anh
                1–3: Hiểu rất kém, lạc đề nhiều
                4–6: Đáp án cơ bản đúng nhưng thiếu chi tiết hoặc có sai sót nhỏ
                7–9: Đáp án đúng, rõ ràng, chi tiết, chỉ còn ít lỗi nhỏ
                10: Hoàn hảo, đầy đủ và chính xác
                Bạn chỉ trả về kết quả là điểm số thực từ 0 đến 10, không giải thích gì thêm.


                Ví dụ:
                1) Q: ""Description your family""
                   Câu trả lời mẫu: ""I have a small but close-knit family. There are four people in my family: my father, my mother, my younger sister, and me. My father is a teacher. He is kind, responsible, and always supports us. My mother is a nurse. She is caring and hardworking, and she always takes good care of our home. My sister is still in high school. She is funny, creative, and we get along very well. We often spend time together, especially on weekends. We watch movies, cook special meals, or go on short trips. My family is very important to me because they always love and support me no matter what.""
                   Trả lời của thí sinh: ""My family is not big, but we are very close. There are four members: my dad, my mom, my younger sister, and I. My dad works as a teacher. He is patient and always encourages me to do my best. My mom is a nurse who works at a local hospital. She’s warm-hearted and keeps everything in the house well organized. My younger sister is still studying in high school. She’s cheerful and loves drawing. Even though she’s younger, we talk and share everything like best friends. We enjoy spending time together when we’re free. On weekends, we usually watch movies, go out for dinner, or visit our grandparents. I really appreciate my family because they always care for me and make me feel safe and loved.""
                   Score: 10

                2) Q: ""Description your family""
                   Câu trả lời mẫu: ""I have a small but close-knit family. There are four people in my family: my father, my mother, my younger sister, and me. My father is a teacher. He is kind, responsible, and always supports us. My mother is a nurse. She is caring and hardworking, and she always takes good care of our home. My sister is still in high school. She is funny, creative, and we get along very well. We often spend time together, especially on weekends. We watch movies, cook special meals, or go on short trips. My family is very important to me because they always love and support me no matter what.""
                   Trả lời của thí sinh: ""Gia đình em không lớn, nhưng rất gắn kết.""
                   Score: 0

                Chỉ trả lời với một số nguyên từ 0 đến 10, không thêm bất kỳ văn bản nào.
                *Lưu ý: Nếu câu trả lời không phải là tiếng anh, mặc định cho 0 điểm

                Q: ""{questionText}""
                Câu trả lời mẫu: ""{correctAnswer}""
                Trả lời của thí sinh: ""{userAnswer}""
            ";

            //var prompt = $@"
            //    Bạn là một giáo viên tiếng Anh chuyên nghiệp. Trước khi chấm, kiểm tra xem “Trả lời của thí sinh” có phải viết bằng tiếng Anh hay không. Nếu không, trả về 0 ngay lập tức mà không cần đánh giá thêm.

            //    Nếu là tiếng Anh, hãy chấm điểm theo **5 tiêu chí** sau, với tổng điểm từ **0 đến 10**, cụ thể như sau:

            //    ---

            //    **1. Task Achievement (0–3 điểm)**  
            //    - 0 điểm: Không trả lời, hoặc trả lời hoàn toàn sai yêu cầu.  
            //    - 1 điểm: Trả lời lệch hướng, không đủ thông tin chính.  
            //    - 2 điểm: Trả lời đúng chủ đề, có ý chính nhưng thiếu chi tiết.  
            //    - 3 điểm: Trả lời đầy đủ yêu cầu, rõ ràng, đúng nội dung đề bài.

            //    ---

            //    **2. Coherence and Cohesion (0–2 điểm)**  
            //    - 0 điểm: Câu rời rạc, không liên kết, trình bày lộn xộn.  
            //    - 1 điểm: Có một số liên kết nhưng thiếu logic hoặc mạch lạc chưa tốt.  
            //    - 2 điểm: Ý kết nối hợp lý, sử dụng từ nối phù hợp, bố cục rõ ràng.

            //    ---

            //    **3. Vocabulary (0–2 điểm)**  
            //    - 0 điểm: Từ vựng quá đơn giản, lặp lại, dùng sai nhiều.  
            //    - 1 điểm: Từ vựng phù hợp nhưng hạn chế hoặc đôi lúc chưa chính xác.  
            //    - 2 điểm: Từ vựng đa dạng, chính xác và phù hợp với nội dung.

            //    ---

            //    **4. Grammar and Accuracy (0–2 điểm)**  
            //    - 0 điểm: Nhiều lỗi ngữ pháp nghiêm trọng gây khó hiểu.  
            //    - 1 điểm: Có lỗi nhỏ về thì, cấu trúc, nhưng không ảnh hưởng nhiều.  
            //    - 2 điểm: Câu đúng ngữ pháp, chỉ có lỗi nhỏ hoặc không đáng kể.

            //    ---

            //    **5. Style and Fluency (0–1 điểm)**  
            //    - 0 điểm: Câu văn gượng gạo, thiếu tự nhiên.  
            //    - 1 điểm: Văn trôi chảy, dễ hiểu, phong cách phù hợp.

            //    ---

            //    **Cách chấm:** Cộng điểm từ 5 tiêu chí trên → trả về **duy nhất một số nguyên từ 0 đến 10**, không thêm lời giải thích nào khác.

            //    ---

            //    **Ví dụ:**

            //    1) Q: ""Describe your family""
            //       Câu trả lời mẫu: ""I have a small but close-knit family. There are four people in my family: my father, my mother, my younger sister, and me. My father is a teacher. He is kind, responsible, and always supports us. My mother is a nurse. She is caring and hardworking, and she always takes good care of our home. My sister is still in high school. She is funny, creative, and we get along very well. We often spend time together, especially on weekends. We watch movies, cook special meals, or go on short trips. My family is very important to me because they always love and support me no matter what.""
            //       Trả lời của thí sinh: ""My family is not big, but we are very close. There are four members: my dad, my mom, my younger sister, and I. My dad works as a teacher. He is patient and always encourages me to do my best. My mom is a nurse who works at a local hospital. She’s warm-hearted and keeps everything in the house well organized. My younger sister is still studying in high school. She’s cheerful and loves drawing. Even though she’s younger, we talk and share everything like best friends. We enjoy spending time together when we’re free. On weekends, we usually watch movies, go out for dinner, or visit our grandparents. I really appreciate my family because they always care for me and make me feel safe and loved.""  
            //       Score: 10

            //    2) Q: ""Describe your family""
            //       Trả lời của thí sinh: ""Gia đình em không lớn, nhưng rất gắn kết.""  
            //       Score: 0

            //    ---

            //    *Lưu ý: Nếu trả lời không phải bằng tiếng Anh, mặc định cho 0 điểm.*

            //    ---

            //    Q: ""{questionText}""
            //    Câu trả lời mẫu: ""{correctAnswer}""
            //    Trả lời của thí sinh: ""{userAnswer}""
            //";

            var fullUrl = $"{_baseUrl}/api/generate";
            var model = _configuration["AI:Ollama:Model"] ?? "mistral:latest";

            var requestBody = new
            {
                model,
                prompt,
                stream = false
            };

            var client = _httpClientFactory.CreateClient();
            var response = await client.PostAsJsonAsync(fullUrl, requestBody, ct);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync(ct);
            var json = JsonDocument.Parse(responseBody);
            var answer = json.RootElement.GetProperty("response").GetString();

            if (float.TryParse(answer?.Trim(), out var score))
            {
                return new GradingResult
                {
                    Score = score,
                    IsCorrect = score >= 6 // ví dụ ngưỡng đúng là >= 6/10
                };
            }

            return new GradingResult { Score = 0, IsCorrect = false };
        }

        public async Task<GradingResult> GradeVoiceAnswerAsync(string questionText, string correctAnswer, string userAnswer, CancellationToken ct = default)
        {
            var prompt = $@"
            Compare the Sample answer and Candidate’s response. 
            - If the Candidate’s response is **identical** to the Sample answer (the same words), always return 10.
            - If the Candidate’s response has the same meaning with only minor acceptable differences, return 7–9.
            - If the response adds, removes, changes, or invents ideas, or is unrelated, give a lower score.
            - If the Candidate’s response is completely unrelated to the Sample answer, return 0.

            Do NOT consider grammar, vocabulary, or pronunciation—score only by similarity in meaning and completeness.

            For example:
            Sample answer: ""The capital of France is Paris.""
            Candidate’s response: ""The capital of France is Paris.""
            Score: 10

            Sample answer: ""I have two brothers.""
            Candidate’s response: ""I have two brothers and a dog.""
            Score: 4

            Sample answer: ""My favorite food is pizza.""
            Candidate’s response: ""My favorite food is noodles.""
            Score: 6

            **Return only a single integer from 0 to 10. Do not return any explanation, text, or percentage.**

            Here are the answers:
            Sample answer: ""{correctAnswer}""
            Candidate’s response: ""{userAnswer}""
            ";

            var fullUrl = $"{_baseUrl}/api/generate";
            var model = _configuration["AI:Ollama:Model"] ?? "mistral:latest";

            var requestBody = new
            {
                model,
                prompt,
                stream = false
            };

            var client = _httpClientFactory.CreateClient();
            var response = await client.PostAsJsonAsync(fullUrl, requestBody, ct);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync(ct);
            var json = JsonDocument.Parse(responseBody);
            var answer = json.RootElement.GetProperty("response").GetString();

            if (float.TryParse(answer?.Trim(), out var score))
            {
                return new GradingResult
                {
                    Score = score,
                    IsCorrect = score >= 6 // ví dụ ngưỡng đúng là >= 6/10
                };
            }

            return new GradingResult { Score = 0, IsCorrect = false };
        }
    }
}
