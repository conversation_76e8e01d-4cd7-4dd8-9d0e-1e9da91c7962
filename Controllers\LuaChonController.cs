﻿using StaffInsightSolution_BE.Infrastructure;
using StaffInsightSolution_BE.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace StaffInsightSolution_BE.Controllers
{
    [EnableCors("CorsApi")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class LuaChonController : ControllerBase
    {
        private readonly IUnitOfWork uow;
        public LuaChonController(IUnitOfWork _uow)
        {
            uow = _uow;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var luaChons = await uow.LuaChons.GetAll_No_ConditionAsync();
            return Ok(luaChons);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var luaChon = await uow.LuaChons.GetByIdAsync(id);
            if (luaChon == null)
            {
                return NotFound();
            }
            return Ok(luaChon);
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] LuaChon luaChon)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdLuaChon = await uow.LuaChons.AddAsync(luaChon);
                await uow.CompleteAsync();

                return CreatedAtAction(nameof(GetById), new { id = luaChon.Id }, luaChon);
            }
            catch (Exception ex)
            {
                // Log exception
                return StatusCode(500, new { message = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] LuaChon luaChon)
        {
            if (id != luaChon.Id)
            {
                return BadRequest("ID không khớp với dữ liệu gửi lên");
            }

            await uow.LuaChons.UpdateAsync(luaChon);
            await uow.CompleteAsync();

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            await uow.LuaChons.DeleteAsync(id);
            await uow.CompleteAsync();

            return NoContent();
        }


        //[HttpPost("CapNhat/{id}")]
        //public ActionResult CapNhatTrangThai(Guid id)
        //{
        //    lock (Commons.LockObjectState)
        //    {
        //        Xe_DongSanPham duLieu = uow.Xe_DongSanPhams.GetById(id);
        //        duLieu.IsSuDung = !duLieu.IsSuDung;
        //        duLieu.UpdatedBy = Guid.Parse(User.Identity.Name);
        //        duLieu.UpdatedDate = DateTime.Now;
        //        uow.Xe_DongSanPhams.Update(duLieu);
        //        uow.CompleteAsync();
        //        if (duLieu.IsSuDung)
        //            return StatusCode(StatusCodes.Status200OK, "Cập nhật Sử dụng thành công");
        //        else return StatusCode(StatusCodes.Status200OK, "Cập nhật Hủy sử dụng thành công");
        //    }
        //}

        //[HttpGet("GetAll")]
        //public ActionResult GetAll(Guid? DonVi_Id)
        //{
        //    Expression<Func<Xe_DongSanPham, bool>> whereFunc = item => !item.IsDeleted && (DonVi_Id == null || item.DonVi_Id == DonVi_Id);
        //    Func<IQueryable<Xe_DongSanPham>, IOrderedQueryable<Xe_DongSanPham>> orderByFunc = item => item.OrderBy(x => x.TenDongSanPham);
        //    string[] includes = { "NhanHieu.LoaiPhuongTien" };
        //    var query = uow.Xe_DongSanPhams.GetAll(whereFunc, orderByFunc, includes);
        //    var data = query.GroupBy(x => x.NhanHieu.LoaiPhuongTien_Id).Select(x => x.First().NhanHieu.LoaiPhuongTien)
        //    .Select(t => new
        //    {
        //        t.Id,
        //        MaDongSanPham = t.MaLoaiPhuongTien,
        //        TenDongSanPham = t.TenLoaiPhuongTien,
        //        IsUsed = false,
        //        Children = query.Where(x => x.NhanHieu.LoaiPhuongTien_Id == t.Id)
        //        .GroupBy(x => x.NhanHieu_Id).Select(x => x.First().NhanHieu)
        //        .Select(x => new
        //        {
        //            x.Id,
        //            MaDongSanPham = x.MaNhanHieu,
        //            TenDongSanPham = x.TenNhanHieu,
        //            IsUsed = false,
        //            Children = query.Where(p => p.NhanHieu_Id == x.Id).Select(p => new
        //            {
        //                p.Id,
        //                p.MaDongSanPham,
        //                p.TenDongSanPham,
        //                IsUsed = true
        //            })
        //        })
        //    });
        //    return Ok(data);
        //}
        //[HttpGet("GetById")]
        //public ActionResult GetById(Guid id)
        //{
        //    string[] includes = { "NhanHieu", "NhanHieu.LoaiPhuongTien" };
        //    var duLieu = uow.Xe_DongSanPhams.GetAll(x => x.Id == id, null, includes).Select(x => new
        //    {
        //        info = x,
        //        x.NhanHieu.LoaiPhuongTien_Id
        //    }).FirstOrDefault();
        //    if (duLieu == null)
        //    {
        //        return NotFound();
        //    }
        //    return Ok(duLieu);
        //}

        //[HttpPost]
        //public ActionResult Post(DongSanPhamAddModel post)
        //{
        //    lock (Commons.LockObjectState)
        //    {
        //        if (!ModelState.IsValid)
        //        {
        //            return BadRequest(ModelState);
        //        }
        //        if (uow.Xe_DongSanPhams.Exists(x => x.MaDongSanPham.Trim().ToLower() == post.MaDongSanPham.Trim().ToLower() && x.DonVi_Id == post.DonVi_Id && !x.IsDeleted))
        //            return BadRequest("Mã " + post.MaDongSanPham + " đã tồn tại trong hệ thống");
        //        Expression<Func<Xe_DongSanPham, bool>> whereFunc = item => !item.IsDeleted && item.NhanHieu_Id == post.NhanHieu_Id;
        //        var max_thutu = uow.Xe_DongSanPhams.GetAll(whereFunc).Max(x => (int?)x.ThuTu) ?? 0;
        //        var duLieu = uow.Xe_DongSanPhams.Add(new Xe_DongSanPham
        //        {
        //            MaDongSanPham = post.MaDongSanPham,
        //            TenDongSanPham = post.TenDongSanPham,
        //            NhanHieu_Id = post.NhanHieu_Id,
        //            DonVi_Id = post.DonVi_Id,
        //            IsSuDung = post.IsSuDung,
        //            CreatedDate = DateTime.Now,
        //            CreatedBy = Guid.Parse(User.Identity.Name)
        //        });
        //        uow.CompleteAsync();
        //        return Ok(duLieu);
        //    }
        //}
        //[HttpPut]
        //public ActionResult Put(Guid id, Xe_DongSanPham duLieu)
        //{
        //    lock (Commons.LockObjectState)
        //    {
        //        if (!ModelState.IsValid)
        //        {
        //            return BadRequest(ModelState);
        //        }
        //        if (id != duLieu.Id)
        //        {
        //            return BadRequest();
        //        }
        //        if (uow.Xe_DongSanPhams.Exists(x => x.MaDongSanPham == duLieu.MaDongSanPham && x.DonVi_Id == duLieu.DonVi_Id && x.Id != id && !x.IsDeleted))
        //            return BadRequest("Mã " + duLieu.MaDongSanPham + " đã tồn tại trong hệ thống");
        //        duLieu.UpdatedBy = Guid.Parse(User.Identity.Name);
        //        duLieu.UpdatedDate = DateTime.Now;
        //        uow.Xe_DongSanPhams.Update(duLieu);
        //        uow.CompleteAsync();
        //        return Ok();
        //    }
        //}
        //[HttpDelete]
        //public ActionResult Delete(Guid id)
        //{
        //    lock (Commons.LockObjectState)
        //    {
        //        Xe_DongSanPham duLieu = uow.Xe_DongSanPhams.GetById(id);
        //        if (duLieu == null)
        //        {
        //            return NotFound();
        //        }
        //        //Check xem có sử dụng hay không
        //        if (uow.Xe_LoaiXes.Exists(x => x.DongSanPham_Id == id && !x.IsDeleted))
        //            return BadRequest(duLieu.TenDongSanPham + " đã sử dụng trong hệ thống");
        //        duLieu.DeletedDate = DateTime.Now;
        //        duLieu.DeletedBy = Guid.Parse(User.Identity.Name);
        //        duLieu.IsDeleted = true;
        //        uow.Xe_DongSanPhams.Update(duLieu);
        //        uow.CompleteAsync();
        //        //Ghi log truy cập
        //        return Ok(duLieu);
        //    }
        //}
    }
}
