﻿using eExamSmart_BE.Models;
using eExamSmart_BE.Services.Interfaces;
using Newtonsoft.Json;
using System.Linq;

namespace eExamSmart_BE.Services.AnserGrader
{
    public class OrderingGrader : IAnswerGrader
    {
        public bool CanHandle(string loaiCauHoi)
        {
            return loaiCauHoi == "ordering";
        }

        public Task<GradingResult> GradeAsync(CauTraLoi ctl, CauHoi cauHoi, CancellationToken cancellationToken)
        {
            var orderingAnswer = JsonConvert.DeserializeObject<List<Guid?>>(ctl.OrderingAnswer ?? "[]");
            var orderingLuaChonIds = cauHoi.LuaChons
                .Where(lc => !lc.IsDeleted)
                .OrderBy(lc => lc.ThuTu)
                .Select(lc => (Guid?)lc.Id)
                .ToList();

            // Kiểm tra độ dài và thứ tự giống nhau
            bool isCorrect = orderingAnswer != null
                && orderingAnswer.Count == orderingLuaChonIds.Count
                && orderingAnswer.SequenceEqual(orderingLuaChonIds); // Kiểm tra thứ tự của các lựa chọn có đúng không

            // Tính điểm
            var score = isCorrect
                ? ctl.CauHoiPhanThiBaiThi?.DiemPhanBo ?? 0
                : 0;

            // Trả về kết quả
            return Task.FromResult(new GradingResult
            {
                Score = score,
                IsCorrect = isCorrect
            });
        }
    }
}
