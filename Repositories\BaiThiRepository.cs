﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface IBaiThiRepository : IRepository<BaiThi>
    {

    }
    public class BaiThiRepository : Repository<BaiThi>, IBaiThiRepository
    {
        public BaiThiRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
