﻿using eExamSmart_BE.MasterDB;
using eExamSmart_BE.MasterDB.Models;
using eExamSmart_BE.Models;
using eExamSmart_BE.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace eExamSmart_BE.Services
{
    public class UserService : IUserService
    {
        private readonly MasterDbContext _masterDb;
        public UserService(MasterDbContext masterDb) => _masterDb = masterDb;

        public async Task<List<AspNetUser>> GetAllAsync(CancellationToken ct = default)
        {
            return await _masterDb.AspNetUsers.ToListAsync(ct);
        }

        public async Task<AspNetUser?> GetByIdAsync(Guid id, CancellationToken ct = default)
        {
            // AsNoTracking tăng performance khi chỉ đọc
            return await _masterDb.AspNetUsers
                .FirstOrDefaultAsync(u => u.Id == id, ct);
        }
    }
}
