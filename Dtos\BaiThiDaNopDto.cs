﻿using StaffInsightSolution_BE.MasterDB.Models;

namespace StaffInsightSolution_BE.Dtos
{
    public class BaiThiDaNopDto
    {
        public Guid Id { get; set; }
        public string MaThiSinh { get; set; }              // Ma của thí sinh
        public string TenThiSinh { get; set; }           // Tên thí sinh
        public string MaDeThi { get; set; }           // Mã đề thi
        public string tenDeThi { get; set; }          // Tên đề thi
        public DateTime ThoiGianBatDau { get; set; }   // Giờ bắt đầu
        public string Duration { get; set; }         // Thời gian làm bài
        public int TotalQuestions { get; set; }        // Tổng số câu
        public int CorrectCount { get; set; }          // Số câu đúng
        public int WrongCount { get; set; }            // Số câu sai
        public int SkippedCount { get; set; }          // Số câu bỏ qua
        public float? TotalScore { get; set; }            // Tổng điểm
        public float MaxScore { get; set; }              // Điểm tối đa của đề thi
        public List<PartDto> PhanThis { get; set; }       // Danh sách theo từng Part
    }

    public class PartDto
    {
        public Guid Id { get; set; }                   // Id của phần thi
        public string TenPhanThi { get; set; }           // Ví dụ: "Part 1"
        public List<QuestionDetailDto> CauHois { get; set; }
    }

    public class QuestionDetailDto
    {
        public Guid Id { get; set; }                   // Id của câu hỏi
        public Guid CauHoiPhanThiBaiThiId { get; set; } // CauHoiPhanThiBaiThiId
        public int Number { get; set; }                // Câu hỏi thứ mấy
        public string? LoaiCauHoi { get; set; }          // Loại câu hỏi
        public string? UserAnswer { get; set; }         // Đáp án của thí sinh
        public string? CorrectAnswer { get; set; }      // Đáp án đúng
        public bool? IsCorrect { get; set; }           // true = đúng, false = sai, null = bỏ qua
        public float? Score { get; set; }               // Điểm của câu hỏi
    }
}
