﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface IRandomRuleTagRepository : IRepository<RandomRuleTag>
    {

    }
    public class RandomRuleTagRepository : Repository<RandomRuleTag>, IRandomRuleTagRepository
    {
        public RandomRuleTagRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
