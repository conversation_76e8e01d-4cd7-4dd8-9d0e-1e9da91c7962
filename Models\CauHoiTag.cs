﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace eExamSmart_BE.Models
{
    public class CauHoiTag : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }
        public Guid CauHoiId { get; set; }
        [ForeignKey(nameof(CauHoiId))]
        public CauHoi? CauHoi { get; set; }

        public Guid TagId { get; set; }
        [ForeignKey(nameof(TagId))]
        public Tag? Tag { get; set; }
    }
}
