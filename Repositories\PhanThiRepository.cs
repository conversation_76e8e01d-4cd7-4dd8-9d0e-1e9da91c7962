﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface IPhanThiRepository : IRepository<PhanThi>
    {

    }
    public class PhanThiRepository : Repository<PhanThi>, IPhanThiRepository
    {
        public PhanThiRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
