<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="EPPlus.Core" Version="1.5.4" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.15" />


		<!-- Authentication & Authorization -->
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.0" />

		<!-- Database -->
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.0" />

		<!-- Common -->
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />

		<PackageReference Include="RabbitMQ.Client" Version="7.1.2" />

	</ItemGroup>
	<ItemGroup>
		<Reference Include="ThacoLibs">
			<HintPath>D:\Thaco_Dev_QLTB\ThacoLibs.dll</HintPath>
		</Reference>
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Data\Migrations\" />
	</ItemGroup>
	<ItemGroup>
		<Content Include="Uploads\**\*.*">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

</Project>
