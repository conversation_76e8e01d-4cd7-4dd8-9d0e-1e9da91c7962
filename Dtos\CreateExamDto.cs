﻿using StaffInsightSolution_BE.Models.Enums;

namespace StaffInsightSolution_BE.Dtos
{
    public class CreateExamDto
    {
        public Guid Id { get; set; }
        public string MaDeThi { get; set; }
        public string TenDeThi { get; set; }
        public string? MoTa { get; set; }
        public int Duration { get; set; }
        public DateTime ThoiGianBatDau { get; set; }
        public DateTime ThoiGianKetThuc { get; set; }
        public DeThiStatus Status { get; set; }
        public List<PhanThiDto> PhanThis { get; set; } = new List<PhanThiDto>();
    }
}
