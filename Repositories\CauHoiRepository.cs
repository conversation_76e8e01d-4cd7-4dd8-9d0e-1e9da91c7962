﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface ICauHoiRepository : IRepository<CauHoi>
    {

    }
    public class CauHoiRepository : Repository<CauHoi>, ICauHoiRepository
    {
        public CauHoiRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
