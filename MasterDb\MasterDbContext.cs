﻿using eExamSmart_BE.MasterDB.Models;
using eExamSmart_BE.Models;
using Microsoft.EntityFrameworkCore;

namespace eExamSmart_BE.MasterDB
{
    public class MasterDbContext : DbContext
    {
        public MasterDbContext(DbContextOptions<MasterDbContext> options) : base(options) { }

        public DbSet<DonVi> DonVis { get; set; }
        public DbSet<BoPhan> BoPhans { get; set; }
        public DbSet<User> ChiTiet_DV_PB_BPs { get; set; }
        public DbSet<AspNetUser> AspNetUsers { get; set; }
    }
}
