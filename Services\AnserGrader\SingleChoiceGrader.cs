﻿using eExamSmart_BE.Models;
using eExamSmart_BE.Services.Interfaces;
using Newtonsoft.Json;

namespace eExamSmart_BE.Services.AnserGrader
{
    public class SingleChoiceGrader : IAnswerGrader
    {
        public bool CanHandle(string loaiCauHoi)
        {
            return loaiCauHoi == "single_choice";
        }

        public Task<GradingResult> GradeAsync(CauTraLoi ctl, CauHoi cauHoi, CancellationToken cancellationToken)
        {
            //throw new NotImplementedException();
            var selectedLuaChonId = JsonConvert.DeserializeObject<Guid?>(ctl.SelectedLuaChonIds ?? "");
            var correctId = cauHoi.LuaChons.FirstOrDefault(lc => lc.IsCorrect && !lc.IsDeleted)?.Id;

            // Kiểm tra đúng/sai
            bool isCorrect = selectedLuaChonId.HasValue && correctId.HasValue && selectedLuaChonId == correctId.Value;

            // T<PERSON>h điểm
            var score = isCorrect
                ? ctl.CauHoiPhanThiBaiThi?.DiemPhanBo ?? 0
                : 0;

            // Trả về kết quả
            return Task.FromResult(new GradingResult
            {
                Score = score,
                IsCorrect = isCorrect
            });
        }
    }
}
