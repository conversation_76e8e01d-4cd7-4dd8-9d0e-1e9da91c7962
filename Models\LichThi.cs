﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace eExamSmart_BE.Models
{
    public class LichThi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        [Required]
        public Guid DeThiId { get; set; }
        [ForeignKey(nameof(DeThiId))]
        public DeThi DeThi { get; set; }

        [Required, MaxLength(200)]
        public string TenLichThi { get; set; }

        [Required]
        public DateTime ThoiGianBatDau { get; set; }

        [Required]
        public DateTime ThoiGianKetThuc { get; set; }

        [MaxLength(200)]
        public string DiaDiem { get; set; }

        [MaxLength(1000)]
        public string MoTa { get; set; }

        public ICollection<BaiThi> BaiThis { get; set; } = new List<BaiThi>();
    }
}