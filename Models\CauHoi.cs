﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace StaffInsightSolution_BE.Models
{
    public class CauHoi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public string NoiDung { get; set; }

        [MaxLength(50)]
        public string LoaiCauH<PERSON> { get; set; }

        [MaxLength(50)]
        public string? MucDo { get; set; }

        [MaxLength(500)]
        public string? HinhAnhUrl { get; set; }

        [MaxLength(500)]
        public string? AmThanhUrl { get; set; }

        public string? DapAnMau { get; set; }

        public ICollection<CauHoiTag> CauHoiTags { get; set; } = new List<CauHoiTag>();
        public ICollection<LuaChon> LuaChons { get; set; } = new List<LuaChon>();
        public ICollection<PhanCauHoi> PhanCauHois { get; set; } = new List<PhanCauHoi>();
    }

}
