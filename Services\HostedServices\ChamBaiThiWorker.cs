﻿using eExamSmart_BE.Services.Interfaces;
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Collections;
using System.Text;

namespace eExamSmart_BE.Services.HostedServices
{
    public class ChamBaiThiWorker : BackgroundService
    {
        private readonly IConnectionFactory _factory;
        private readonly ILogger<ChamBaiThiWorker> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private const int MAX_RETRY = 5;

        public ChamBaiThiWorker(IConnectionFactory factory, ILogger<ChamBaiThiWorker> logger, IServiceScopeFactory scopeFactory)
        {
            _factory = factory;
            _logger = logger;
            _scopeFactory = scopeFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // Tạo connection + channel 1 lần ở Start
            var connection = await _factory.CreateConnectionAsync(stoppingToken);
            var channel = await connection.CreateChannelAsync(cancellationToken: stoppingToken);

            // Chỉ nhận từng message 1 (fair dispatch)
            await channel.BasicQosAsync(prefetchSize: 0, prefetchCount: 1, global: false, cancellationToken: stoppingToken);

            // Đăng ký consumer bất đồng bộ
            var consumer = new AsyncEventingBasicConsumer(channel);
            consumer.ReceivedAsync += async (model, ea) =>
            {
                _ = Task.Run(async () =>
                {
                    using var scope = _scopeFactory.CreateScope();
                    var grader = scope.ServiceProvider.GetRequiredService<IExamGrader>();
                    var body = ea.Body.ToArray();
                    var json = Encoding.UTF8.GetString(body);
                    var msgObj = JsonConvert.DeserializeObject<BaiThiMessage>(json);

                    // Đếm số lần đã retry qua header "x-death"
                    int retryCount = 0;
                    if (ea.BasicProperties.Headers != null
                        && ea.BasicProperties.Headers.TryGetValue("x-death", out var xDeathObj)
                        && xDeathObj is IList xDeathList)
                    {
                        foreach(var raw in xDeathList.Cast<Dictionary<string, object>>())
                        {
                            var qBytes = raw["queue"] as byte[];
                            var queueName = Encoding.UTF8.GetString(qBytes!);
                            if (queueName == "grading_queue" && raw.TryGetValue("count", out var countObj) && countObj is long count)
                            {
                                retryCount = (int)count;
                                break;
                            }
                        }
                    }

                    try
                    {
                        _logger.LogInformation($"[Grade] Bài thi {msgObj.BaiThiId}, Lần #{retryCount + 1}");
                        await grader.GradeExamAsync(msgObj.BaiThiId, stoppingToken);

                        // Thành công → ACK để xóa message
                        await channel.BasicAckAsync(ea.DeliveryTag, false);
                    }
                    catch (Exception ex)
                    {
                        if (retryCount >= MAX_RETRY)
                        {
                            // Quá số lần retry → gửi vào DLQ
                            _logger.LogError(ex, $"[DLQ] Bài thi {msgObj.BaiThiId} failed after {retryCount} retries");
                            var dlqProps = new BasicProperties { Persistent = true };
                            dlqProps.Persistent = true;
                            await channel.BasicPublishAsync(
                                exchange: "dlx_exchange",
                                routingKey: "grading_queue",
                                mandatory: true,
                                basicProperties: dlqProps,
                                body: body
                            );
                            await channel.BasicAckAsync(ea.DeliveryTag, false);
                        }
                        else
                        {
                            // Chưa hết retry → reject để dead-letter vào retry_exchange
                            _logger.LogWarning(ex, $"[Retry#{retryCount + 1}] Bài thi {msgObj.BaiThiId} lỗi, sẽ retry");
                            await channel.BasicRejectAsync(ea.DeliveryTag, requeue: false);
                        }
                    }
                }, stoppingToken);
            };

            await channel.BasicConsumeAsync(
                queue: "grading_queue",
                autoAck: false,    // chúng ta ack thủ công
                consumer: consumer
            );
        }

        private class BaiThiMessage
        {
            public Guid BaiThiId { get; set; }
        }
    }
}
