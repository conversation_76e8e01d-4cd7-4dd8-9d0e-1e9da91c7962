using eExamSmart_BE.Dtos;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;
using eExamSmart_BE.Models.Enums;
using eExamSmart_BE.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace eExamSmart_BE.Services.Implementations
{
    public class DeThiService : IDeThiService
    {
        private readonly IUnitOfWork _uow;

        public DeThiService(IUnitOfWork uow)
        {
            _uow = uow;
        }

        public async Task<Guid> CreateWithDetailsAsync(CreateExamDto dto)
        {
            if (await _uow.DeThis.ExistsAsync(d => d.MaDeThi == dto.MaDeThi))
                throw new InvalidOperationException("Mã đề đã tồn tại.");

            var deThi = new DeThi
            {
                Id = Guid.NewGuid(),
                MaDeThi = dto.MaDeThi,
                TenDeThi = dto.TenDeThi,
                MoTa = dto.MoTa,
                Duration = dto.Duration,
                ThoiGianBatDau = dto.ThoiGianBatDau,
                ThoiGianKetThuc = dto.ThoiGianKetThuc,
                Status = dto.Status
            };

            var phanThisList = new List<PhanThi>();
            var cauHoiList = new List<CauHoi>();
            var luaChonList = new List<LuaChon>();
            var phanCauHoiList = new List<PhanCauHoi>();
            var randomRulesList = new List<RandomRule>();

            foreach (var ptDto in dto.PhanThis)
            {
                var phanThi = new PhanThi
                {
                    Id = Guid.NewGuid(),
                    DeThiId = deThi.Id,
                    TenPhanThi = ptDto.TenPhanThi,
                    ThuTu = ptDto.ThuTu,
                    KyNang = ptDto.KyNang
                };
                phanThisList.Add(phanThi);

                if (ptDto.IsRandom && ptDto.RandomRule != null)
                {
                    var r = ptDto.RandomRule;

                    if (r.LoaiCauHoi == "true_false" && r.SoLuongMenhDe is null)
                        throw new InvalidOperationException($"Phần \"{ptDto.TenPhanThi}\" thiếu số lượng mệnh đề.");

                    var requiredTagIds = r.TagIds ?? new List<Guid>();
                    var candidatesQuery = _uow.CauHois.Query(c => !c.IsDeleted);
                    if (r.LoaiCauHoi != null)
                        candidatesQuery = candidatesQuery.Where(c => c.LoaiCauHoi == r.LoaiCauHoi);
                    if (requiredTagIds.Count > 0)
                    {
                        candidatesQuery = candidatesQuery.Where(c =>
                            c.CauHoiTags.Count(ct => !ct.IsDeleted && requiredTagIds.Contains(ct.TagId)) == requiredTagIds.Count);
                    }
                    if (string.Equals(r.LoaiCauHoi, "true_false", StringComparison.OrdinalIgnoreCase))
                    {
                        candidatesQuery = candidatesQuery.Where(c => c.LuaChons.Count(lc => !lc.IsDeleted) >= r.SoLuongMenhDe);
                    }
                    var candidatesCount = await candidatesQuery.CountAsync();
                    if (candidatesCount < r.SoLuong)
                    {
                        throw new InvalidOperationException($"Phần thi \"{ptDto.TenPhanThi}\" yêu cần {r.SoLuong} câu, nhưng chỉ có {candidatesCount} câu hỏi thỏa điều kiện random.");
                    }

                    var rr = new RandomRule
                    {
                        Id = Guid.NewGuid(),
                        PhanThiId = phanThi.Id,
                        SoLuong = r.SoLuong,
                        LoaiCauHoi = r.LoaiCauHoi,
                        MucDo = r.MucDo,
                        Diem = r.Diem,
                        SoLuongMenhDe = r.SoLuongMenhDe
                    };
                    foreach (var tagId in r.TagIds ?? Enumerable.Empty<Guid>())
                    {
                        rr.RandomRuleTags.Add(new RandomRuleTag
                        {
                            RandomRuleId = rr.Id,
                            TagId = tagId
                        });
                    }
                    randomRulesList.Add(rr);
                }

                foreach (var chDto in ptDto.CauHoi)
                {
                    var cauHoi = new CauHoi
                    {
                        Id = Guid.NewGuid(),
                        NoiDung = chDto.NoiDung,
                        LoaiCauHoi = chDto.LoaiCauHoi,
                        MucDo = chDto.MucDo,
                        HinhAnhUrl = chDto.HinhAnhUrl,
                        AmThanhUrl = chDto.AmThanhUrl,
                    };
                    cauHoiList.Add(cauHoi);

                    foreach (var lcDto in chDto.LuaChon)
                    {
                        var luaChon = new LuaChon
                        {
                            Id = Guid.NewGuid(),
                            CauHoiId = cauHoi.Id,
                            NoiDung = lcDto.NoiDung,
                            IsCorrect = lcDto.IsCorrect,
                            ThuTu = lcDto.ThuTu
                        };
                        luaChonList.Add(luaChon);
                    }

                    var phanCauHoi = new PhanCauHoi
                    {
                        Id = Guid.NewGuid(),
                        PhanThiId = phanThi.Id,
                        CauHoiId = cauHoi.Id,
                        ThuTu = chDto.ThuTu,
                        Diem = chDto.DiemPhanHoi
                    };
                    phanCauHoiList.Add(phanCauHoi);
                }
            }

            await _uow.DeThis.AddAsync(deThi);
            await _uow.PhanThis.AddRangeAsync(phanThisList);
            await _uow.CauHois.AddRangeAsync(cauHoiList);
            await _uow.LuaChons.AddRangeAsync(luaChonList);
            await _uow.PhanCauHois.AddRangeAsync(phanCauHoiList);
            await _uow.RandomRules.AddRangeAsync(randomRulesList);

            await _uow.CompleteAsync();
            return deThi.Id;
        }

        public async Task UpdateWithDetailsAsync(Guid id, CreateExamDto dto)
        {
            var deThi = await _uow.DeThis.GetSingleAsync(
                d => d.Id == id && !d.IsDeleted,
                includes: new[] { "PhanThis.PhanCauHois.CauHoi.LuaChons", "PhanThis.RandomRules.RandomRuleTags" });

            if (deThi == null)
                throw new KeyNotFoundException();

            deThi.MaDeThi = dto.MaDeThi;
            deThi.TenDeThi = dto.TenDeThi;
            deThi.MoTa = dto.MoTa;
            deThi.Duration = dto.Duration;
            deThi.ThoiGianBatDau = dto.ThoiGianBatDau;
            deThi.ThoiGianKetThuc = dto.ThoiGianKetThuc;
            deThi.Status = dto.Status;

            var existingPhanThis = deThi.PhanThis.ToList();
            foreach (var phanThiDto in dto.PhanThis)
            {
                var phanThi = existingPhanThis.FirstOrDefault(ep => ep.Id == phanThiDto.Id);
                if (phanThi == null)
                {
                    phanThi = new PhanThi
                    {
                        Id = Guid.NewGuid(),
                        DeThiId = deThi.Id,
                        TenPhanThi = phanThiDto.TenPhanThi,
                        ThuTu = phanThiDto.ThuTu,
                        KyNang = phanThiDto.KyNang
                    };
                    await _uow.PhanThis.AddAsync(phanThi);
                    existingPhanThis.Add(phanThi);
                }
                else
                {
                    phanThi.TenPhanThi = phanThiDto.TenPhanThi;
                    phanThi.ThuTu = phanThiDto.ThuTu;
                    phanThi.KyNang = phanThiDto.KyNang;
                }

                var existingCauHois = phanThi.PhanCauHois.ToList();
                foreach (var cauHoiDto in phanThiDto.CauHoi)
                {
                    var phanCauHoi = existingCauHois.FirstOrDefault(ch => ch.Id == cauHoiDto.Id);
                    if (phanCauHoi != null)
                    {
                        var cauHoi = phanCauHoi.CauHoi;
                        cauHoi.NoiDung = cauHoiDto.NoiDung;
                        cauHoi.LoaiCauHoi = cauHoiDto.LoaiCauHoi;
                        cauHoi.MucDo = cauHoiDto.MucDo;
                        cauHoi.HinhAnhUrl = cauHoiDto.HinhAnhUrl;
                        cauHoi.AmThanhUrl = cauHoiDto.AmThanhUrl;
                        var existingLuaChons = cauHoi.LuaChons.ToList();
                        var luaChonDtos = cauHoiDto.LuaChon ?? new List<LuaChonDto>();
                        foreach (var lc in existingLuaChons.Where(l => luaChonDtos.All(dto => dto.Id != l.Id)))
                            await _uow.LuaChons.DeleteAsync(lc.Id);
                        foreach (var luaChonDto in luaChonDtos.Where(dto => existingLuaChons.All(l => l.Id != dto.Id)))
                        {
                            var luaChon = new LuaChon
                            {
                                Id = Guid.NewGuid(),
                                CauHoiId = cauHoi.Id,
                                NoiDung = luaChonDto.NoiDung,
                                IsCorrect = luaChonDto.IsCorrect,
                                ThuTu = luaChonDto.ThuTu
                            };
                            await _uow.LuaChons.AddAsync(luaChon);
                        }
                        phanCauHoi.ThuTu = cauHoiDto.ThuTu;
                        phanCauHoi.Diem = cauHoiDto.DiemPhanHoi;
                    }
                    else
                    {
                        var cauHoi = new CauHoi
                        {
                            Id = Guid.NewGuid(),
                            NoiDung = cauHoiDto.NoiDung,
                            LoaiCauHoi = cauHoiDto.LoaiCauHoi,
                            MucDo = cauHoiDto.MucDo,
                            HinhAnhUrl = cauHoiDto.HinhAnhUrl,
                            AmThanhUrl = cauHoiDto.AmThanhUrl,
                        };
                        await _uow.CauHois.AddAsync(cauHoi);
                        foreach (var luaChonDto in cauHoiDto.LuaChon)
                        {
                            var luaChon = new LuaChon
                            {
                                Id = Guid.NewGuid(),
                                CauHoiId = cauHoi.Id,
                                NoiDung = luaChonDto.NoiDung,
                                IsCorrect = luaChonDto.IsCorrect,
                                ThuTu = luaChonDto.ThuTu
                            };
                            await _uow.LuaChons.AddAsync(luaChon);
                        }
                        var newPhanCauHoi = new PhanCauHoi
                        {
                            Id = Guid.NewGuid(),
                            PhanThiId = phanThi.Id,
                            CauHoiId = cauHoi.Id,
                            ThuTu = cauHoiDto.ThuTu,
                            Diem = cauHoiDto.DiemPhanHoi
                        };
                        await _uow.PhanCauHois.AddAsync(newPhanCauHoi);
                    }
                }

                var existingRules = phanThi.RandomRules.ToList();
                var randomRuleDto = phanThiDto.RandomRule;

                if (randomRuleDto == null || !phanThiDto.IsRandom)
                {
                    foreach (var rr in existingRules)
                    {
                        foreach (var rt in rr.RandomRuleTags)
                            await _uow.RandomRuleTags.DeleteAsync(rt.Id);
                        await _uow.RandomRules.DeleteAsync(rr.Id);
                    }
                }
                else
                {
                    var requiredTagIds = randomRuleDto.TagIds ?? new List<Guid>();
                    var candidatesQuery = _uow.CauHois.Query(c => !c.IsDeleted);
                    if (randomRuleDto.LoaiCauHoi != null)
                        candidatesQuery = candidatesQuery.Where(c => c.LoaiCauHoi == randomRuleDto.LoaiCauHoi);
                    if (requiredTagIds.Count > 0)
                    {
                        candidatesQuery = candidatesQuery.Where(c =>
                            c.CauHoiTags.Count(ct => !ct.IsDeleted && requiredTagIds.Contains(ct.TagId)) == requiredTagIds.Count);
                    }
                    if (string.Equals(randomRuleDto.LoaiCauHoi, "true_false", StringComparison.OrdinalIgnoreCase))
                    {
                        candidatesQuery = candidatesQuery.Where(c => c.LuaChons.Count(lc => !lc.IsDeleted) >= randomRuleDto.SoLuongMenhDe);
                    }
                    var candidatesCount = await candidatesQuery.CountAsync();
                    if (candidatesCount < randomRuleDto.SoLuong)
                    {
                        throw new InvalidOperationException($"Phần thi \"{phanThiDto.TenPhanThi}\" yêu cần {randomRuleDto.SoLuong} câu, nhưng chỉ có {candidatesCount} câu hỏi thỏa điều kiện random.");
                    }

                    if (existingRules.Any())
                    {
                        var rrEntity = existingRules.First();
                        rrEntity.SoLuong = randomRuleDto.SoLuong;
                        rrEntity.LoaiCauHoi = randomRuleDto.LoaiCauHoi;
                        rrEntity.MucDo = randomRuleDto.MucDo;
                        rrEntity.Diem = randomRuleDto.Diem;
                        rrEntity.SoLuongMenhDe = randomRuleDto.SoLuongMenhDe;

                        var existingTags = rrEntity.RandomRuleTags.Where(et => !et.IsDeleted);
                        var dtoTagIds = randomRuleDto.TagIds ?? new List<Guid>();
                        foreach (var rt in existingTags.Where(et => !dtoTagIds.Contains(et.TagId)))
                            await _uow.RandomRuleTags.DeleteAsync(rt.Id);
                        foreach (var newTagId in dtoTagIds.Where(id => existingTags.All(et => et.TagId != id)))
                        {
                            await _uow.RandomRuleTags.AddAsync(new RandomRuleTag
                            {
                                RandomRuleId = rrEntity.Id,
                                TagId = newTagId
                            });
                        }
                    }
                    else
                    {
                        var rrEntity = new RandomRule
                        {
                            Id = Guid.NewGuid(),
                            PhanThiId = phanThi.Id,
                            SoLuong = randomRuleDto.SoLuong,
                            LoaiCauHoi = randomRuleDto.LoaiCauHoi,
                            MucDo = randomRuleDto.MucDo,
                            Diem = randomRuleDto.Diem,
                            SoLuongMenhDe = randomRuleDto.SoLuongMenhDe
                        };
                        foreach (var tagId in randomRuleDto.TagIds ?? Enumerable.Empty<Guid>())
                        {
                            rrEntity.RandomRuleTags.Add(new RandomRuleTag
                            {
                                RandomRuleId = rrEntity.Id,
                                TagId = tagId
                            });
                        }
                        await _uow.RandomRules.AddAsync(rrEntity);
                    }
                }
            }

            await _uow.CompleteAsync();
        }
    }
}
