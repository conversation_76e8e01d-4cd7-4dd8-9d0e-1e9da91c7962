﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface ILuaChonRepository : IRepository<LuaChon>
    {

    }
    public class LuaChonRepository : Repository<LuaChon>, ILuaChonRepository
    {
        public LuaChonRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
