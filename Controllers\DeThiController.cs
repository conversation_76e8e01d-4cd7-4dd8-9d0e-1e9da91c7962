﻿using eExamSmart_BE.Dtos;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models.Enums;
using eExamSmart_BE.Services;
using eExamSmart_BE.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace eExamSmart_BE.Controllers
{
    [EnableCors("CorsApi")]
    [Authorize]
    [Route("api/EES/[controller]")]
    [ApiController]
    public class DeThiController : ControllerBase
    {
        private readonly IUnitOfWork uow;
        private readonly ICurrentUserService currentUserService;
        private readonly IDeThiService _deThiService;


        public DeThiController(IUnitOfWork _uow, ICurrentUserService currentUserService, IDeThiService deThiService)
        {
            uow = _uow;
            this.currentUserService = currentUserService;
            _deThiService = deThiService;
        }

        // GET: api/EES/DeThi
        /// <summary>
        /// Lấy danh sách DeThi kèm thời gian thi và tổng số câu hỏi (không dùng DTO)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAllSummary()
        {
            // Lấy tất cả đề thi chưa xóa, kèm luôn PhanThis, PhanCauHoi, RandomRule
            var deThisList = await uow.DeThis.GetAllAsync(
                d => !d.IsDeleted,
                orderBy: q => q.OrderBy(d => d.TenDeThi),
                includes: new[] { "PhanThis.PhanCauHois", "PhanThis.RandomRules" }
            );

            var result = deThisList.Select(dt => new
            {
                dt.Id,
                dt.MaDeThi,
                dt.TenDeThi,
                dt.MoTa,
                dt.Duration,
                ThoiGianBatDau = dt.ThoiGianBatDau.ToString("HH:mm - dd'/'MM'/'yyyy"),
                ThoiGianKetThuc = dt.ThoiGianKetThuc.ToString("HH:mm - dd'/'MM'/'yyyy"),
                // Tính tổng câu hỏi ngay trên object đã include
                TotalQuestions = dt.PhanThis.Sum(pt => pt.PhanCauHois.Count + pt.RandomRules.Sum(r => r.SoLuong))
            });

            return Ok(result);
        }

        // GET: api/EES/DeThi
        /// <summary>
        /// Lấy danh sách DeThi đang soạn kèm thời gian thi và tổng số câu hỏi (không dùng DTO)
        /// </summary>
        [HttpGet("dang-soan")]
        public async Task<IActionResult> GetDangSoanSummary()
        {
            // Lấy tất cả đề thi chưa xóa, kèm luôn PhanThis, PhanCauHoi, RandomRule
            var deThisList = await uow.DeThis.GetAllAsync(
                d => !d.IsDeleted
                && d.Status == DeThiStatus.DangSoan,
                orderBy: q => q.OrderByDescending(d => d.UpdatedDate ?? d.CreatedDate),
                includes: new[] { "PhanThis.PhanCauHois", "PhanThis.RandomRules" }
            );

            var result = deThisList.Select(dt => new
            {
                dt.Id,
                dt.MaDeThi,
                dt.TenDeThi,
                dt.MoTa,
                dt.Duration,
                ThoiGianBatDau = dt.ThoiGianBatDau.ToString("HH:mm - dd'/'MM'/'yyyy"),
                ThoiGianKetThuc = dt.ThoiGianKetThuc.ToString("HH:mm - dd'/'MM'/'yyyy"),
                Status = DeThiStatus.DangSoan,
                // Tính tổng câu hỏi ngay trên object đã include
                TotalQuestions = dt.PhanThis.Sum(pt => pt.PhanCauHois.Count + pt.RandomRules.Sum(r => r.SoLuong))
            });

            return Ok(result);
        }

        //// GET: api/EES/DeThi
        ///// <summary>
        ///// Lấy danh sách DeThi đã đăng ra kèm thời gian thi và tổng số câu hỏi (không dùng DTO)
        ///// </summary>
        //[HttpGet("DaDang")]
        //public async Task<IActionResult> GetDaDangSummary()
        //{
        //    var now = DateTime.Now;
        //    // Lấy tất cả đề thi chưa xóa, kèm luôn PhanThis, PhanCauHoi, RandomRule
        //    var deThisList = await uow.DeThis.GetAllAsync(
        //        d => !d.IsDeleted
        //        && d.Status == DeThiStatus.DaDang
        //        && d.ThoiGianBatDau ,
        //        orderBy: q => q.OrderBy(d => d.TenDeThi),
        //        includes: new[] { "PhanThis.PhanCauHois", "PhanThis.RandomRules" }
        //    );

        //    var result = deThisList.Select(dt => new
        //    {
        //        dt.Id,
        //        dt.MaDeThi,
        //        dt.TenDeThi,
        //        dt.MoTa,
        //        dt.Duration,
        //        ThoiGianBatDau = dt.ThoiGianBatDau.ToString("HH:mm - dd'/'MM'/'yyyy"),
        //        ThoiGianKetThuc = dt.ThoiGianKetThuc.ToString("HH:mm - dd'/'MM'/'yyyy"),
        //        // Tính tổng câu hỏi ngay trên object đã include
        //        TotalQuestions = dt.PhanThis.Sum(pt => pt.PhanCauHois.Count + pt.RandomRules.Sum(r => r.SoLuong))
        //    });

        //    return Ok(result);
        //}

        // GET: api/EES/DeThi
        /// <summary>
        /// Lấy danh sách DeThi đang diễn ra kèm thời gian thi và tổng số câu hỏi (không dùng DTO)
        /// </summary>
        [HttpGet("dang-dien-ra")]
        public async Task<IActionResult> GetDangDienRaSummary()
        {
            var now = DateTime.Now;
            // Lấy tất cả đề thi chưa xóa, kèm luôn PhanThis, PhanCauHoi, RandomRule
            var deThisList = await uow.DeThis.GetAllAsync(
                d => !d.IsDeleted
                && d.Status == DeThiStatus.DaDienRa
                && now <= d.ThoiGianKetThuc,
                orderBy: q => q.OrderByDescending(d => d.UpdatedDate ?? d.CreatedDate),
                includes: new[] { "PhanThis.PhanCauHois", "PhanThis.RandomRules" }
            );

            var result = deThisList.Select(dt => new
            {
                dt.Id,
                dt.MaDeThi,
                dt.TenDeThi,
                dt.MoTa,
                dt.Duration,
                ThoiGianBatDau = dt.ThoiGianBatDau.ToString("HH:mm - dd'/'MM'/'yyyy"),
                ThoiGianKetThuc = dt.ThoiGianKetThuc.ToString("HH:mm - dd'/'MM'/'yyyy"),
                Status = DeThiStatus.DaDienRa,
                // Tính tổng câu hỏi ngay trên object đã include
                TotalQuestions = dt.PhanThis.Sum(pt => pt.PhanCauHois.Count + pt.RandomRules.Sum(r => r.SoLuong))
            });

            return Ok(result);
        }

        [HttpGet("da-ket-thuc")]
        public async Task<IActionResult> GetDaKetThucSummary()
        {
            var now = DateTime.Now;
            // Lấy tất cả đề thi chưa xóa, kèm luôn PhanThis, PhanCauHoi, RandomRule
            var deThisList = await uow.DeThis.GetAllAsync(
                d => !d.IsDeleted
                && (d.Status == DeThiStatus.HetHan || (d.Status == DeThiStatus.DaDienRa && now > d.ThoiGianKetThuc)),
                orderBy: q => q.OrderBy(d => d.TenDeThi),
                includes: new[] { "PhanThis.PhanCauHois", "PhanThis.RandomRules" }
            );

            var result = deThisList.Select(dt => new
            {
                dt.Id,
                dt.MaDeThi,
                dt.TenDeThi,
                dt.MoTa,
                dt.Duration,
                ThoiGianBatDau = dt.ThoiGianBatDau.ToString("HH:mm - dd'/'MM'/'yyyy"),
                ThoiGianKetThuc = dt.ThoiGianKetThuc.ToString("HH:mm - dd'/'MM'/'yyyy"),
                Status = DeThiStatus.HetHan,
                // Tính tổng câu hỏi ngay trên object đã include
                TotalQuestions = dt.PhanThis.Sum(pt => pt.PhanCauHois.Count + pt.RandomRules.Sum(r => r.SoLuong))
            });

            return Ok(result);
        }

        // GET: api/EES/DeThi
        /// <summary>
        /// Lấy danh sách DeThi đang diễn ra theo user kèm thời gian thi và tổng số câu hỏi (không dùng DTO)
        /// </summary>
        [HttpGet("dang-dien-ra-theo-nguoi-dung")]
        public async Task<IActionResult> GetDangDienRaUserSummary()
        {
            var now = DateTime.Now;
            //var userId = HttpContext.User.FindFirst(ClaimTypes.Name)?.Value;

            var userId = currentUserService.GetCurrentUserId();

            // Lấy tất cả đề thi chưa xóa, kèm luôn PhanThis, PhanCauHoi, RandomRule
            var deThis = await uow.DeThis.GetAllAsync(
                d => !d.IsDeleted
                && d.Status == DeThiStatus.DaDienRa
                && !d.BaiThis.Any(bt => bt.UserId == userId && bt.ThoiGianKetThuc != null && !bt.IsDeleted),
                //&& now <= d.ThoiGianKetThuc,
                orderBy: q => q.OrderBy(d => d.TenDeThi),
                includes: new[] { "PhanThis.PhanCauHois", "PhanThis.RandomRules" }
            );

            var result = deThis.Select(dt => new
            {
                dt.Id,
                dt.MaDeThi,
                dt.TenDeThi,
                dt.MoTa,
                dt.Duration,
                ThoiGianBatDau = dt.ThoiGianBatDau.ToString("HH:mm - dd'/'MM'/'yyyy"),
                ThoiGianKetThuc = dt.ThoiGianKetThuc.ToString("HH:mm - dd'/'MM'/'yyyy"),
                // Tính tổng câu hỏi ngay trên object đã include
                TotalQuestions = dt.PhanThis.Sum(pt => pt.PhanCauHois.Count + pt.RandomRules.Sum(r => r.SoLuong))
            });

            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var deThi = await uow.DeThis.GetSingleAsync(
                d => d.Id == id && !d.IsDeleted ,
                includes: new[]
                {
                    "PhanThis.PhanCauHois.CauHoi.LuaChons",
                    "PhanThis.RandomRules",
                    "PhanThis.RandomRules.RandomRuleTags"
                }
            );

            if (deThi == null) return NotFound();


            // Sử dụng lại CreateExamDto làm response
            var dto = new CreateExamDto
            {
                Id = id,
                MaDeThi = deThi.MaDeThi,
                TenDeThi = deThi.TenDeThi,
                MoTa = deThi.MoTa,
                Duration = deThi.Duration,
                ThoiGianBatDau = deThi.ThoiGianBatDau,
                ThoiGianKetThuc = deThi.ThoiGianKetThuc,
                Status = deThi.Status,
                PhanThis = deThi.PhanThis
                    .Where(pt => !pt.IsDeleted)
                    .OrderBy(pt => pt.ThuTu)
                    .Select(pt => new PhanThiDto
                    {
                        Id = pt.Id,
                        TenPhanThi = pt.TenPhanThi,
                        ThuTu = pt.ThuTu,
                        KyNang = pt.KyNang,
                        IsRandom = pt.RandomRules.Where(rr => !rr.IsDeleted).Any(),
                        RandomRule = pt.RandomRules
                            .Where(rr => !rr.IsDeleted)
                            .Select(rr => new RandomRuleDto
                            {
                                Id = rr.Id,
                                Diem = rr.Diem,
                                LoaiCauHoi = rr.LoaiCauHoi,
                                MucDo = rr.MucDo,
                                SoLuong = rr.SoLuong,
                                SoLuongMenhDe = rr.SoLuongMenhDe,
                                TagIds = rr.RandomRuleTags.Where(rt => !rt.IsDeleted).Select(rt => rt.TagId).ToList()
                            })
                            .FirstOrDefault(),
                        CauHoi = pt.PhanCauHois
                            .Where(pch => !pch.IsDeleted)
                            .OrderBy(pch => pch.ThuTu)
                            .Select(pch => new CauHoiDto
                            {
                                Id = pch.Id,
                                NoiDung = pch.CauHoi.NoiDung,
                                LoaiCauHoi = pch.CauHoi.LoaiCauHoi,
                                MucDo = pch.CauHoi.MucDo,
                                HinhAnhUrl = pch.CauHoi.HinhAnhUrl,
                                AmThanhUrl = pch.CauHoi.AmThanhUrl,
                                ThuTu = pch.ThuTu,
                                DiemPhanHoi = (int)pch.Diem,
                                LuaChon = pch.CauHoi.LuaChons
                                    .Where(pt => !pt.IsDeleted)
                                    .OrderBy(lc => lc.ThuTu)
                                    .Select(lc => new LuaChonDto
                                    {
                                        Id = lc.Id,
                                        NoiDung = lc.NoiDung,
                                        IsCorrect = lc.IsCorrect,
                                        ThuTu = lc.ThuTu
                                    })
                                    .ToList()
                            })
                            .ToList()
                    })
                    .ToList()
            };

            return Ok(dto);
        }

        // POST: api/EES/DeThi
        /// <summary>
        /// Tạo mới DeThi cùng các PhanThi, CauHoi và LuaChon (không tạo RandomRule)
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreateWithDetails([FromBody] CreateExamDto dto)
        {
            try
            {
                var id = await _deThiService.CreateWithDetailsAsync(dto);
                return CreatedAtAction(nameof(GetAllSummary), new { id }, null);
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
        }

        /// <summary>
        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateWithDetails(Guid id, [FromBody] CreateExamDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);
            try
            {
                await _deThiService.UpdateWithDetailsAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
        }


        // DELETE: api/EES/DeThi/{id}
        /// <summary>
        /// Xóa thực sự DeThi cùng các PhanThi và PhanCauHoi liên quan, giữ lại CauHoi và LuaChon
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            // Lấy đề thi kèm các phần thi và liên kết câu hỏi
            var deThi = await uow.DeThis.GetSingleAsync(
                d => d.Id == id && !d.IsDeleted,
                includes: new[] { "PhanThis.PhanCauHois" }
            );
            if (deThi == null)
                return NotFound();

            // Tập hợp IDs các PhanCauHoi và PhanThi cần xóa
            var phanCauHoiIds = deThi.PhanThis
                .SelectMany(pt => pt.PhanCauHois)
                .Select(pch => (object)pch.Id)
                .ToList();
            var phanThiIds = deThi.PhanThis
                .Select(pt => (object)pt.Id)
                .ToList();

            // Xóa batch PhanCauHoi rồi PhanThi
            if (phanCauHoiIds.Any())
                await uow.PhanCauHois.DeleteRangeAsync(phanCauHoiIds);
            if (phanThiIds.Any())
                await uow.PhanThis.DeleteRangeAsync(phanThiIds);

            // Cuối cùng xóa DeThi
            await uow.DeThis.DeleteAsync(id);
            await uow.CompleteAsync();

            return NoContent();
        }
    }
}
