﻿namespace eExamSmart_BE.Dtos
{
    public class RandomRuleDto
    {
        public Guid Id { get; set; }
        public int SoLuong { get; set; }
        public string? LoaiCauHoi { get; set; }
        public string? MucDo { get; set; }
        public float Diem { get; set; }
        public int? SoLuongMenhDe { get; set; } // số mệnh đề nếu là loại reading comprehension
        public List<Guid> TagIds { get; set; } = new List<Guid>();
    }
}
