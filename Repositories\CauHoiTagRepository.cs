﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface ICauHoiTagRepository : IRepository<CauHoiTag>
    {

    }
    public class CauHoiTagRepository : Repository<CauHoiTag>, ICauHoiTagRepository
    {
        public CauHoiTagRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
