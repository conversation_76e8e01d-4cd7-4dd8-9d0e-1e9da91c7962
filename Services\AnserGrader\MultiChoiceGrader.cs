﻿using eExamSmart_BE.Models;
using eExamSmart_BE.Services.Interfaces;
using Newtonsoft.Json;

namespace eExamSmart_BE.Services.AnserGrader
{
    public class MultiChoiceGrader : IAnswerGrader
    {
        public bool CanHandle(string loaiCauHoi)
        {
            return loaiCauHoi == "multiple_choice";
        }

        public Task<GradingResult> GradeAsync(CauTraLoi ctl, CauHoi cauHoi, CancellationToken cancellationToken)
        {
            var selectedLuaChonIds = JsonConvert.DeserializeObject<List<Guid?>>(ctl.SelectedLuaChonIds ?? "[]");
            var correctIds = cauHoi.LuaChons.Where(lc => lc.IsCorrect && !lc.IsDeleted).Select(lc => lc.Id);

            // Kiểm tra đúng/sai
            bool isCorrect = correctIds.Count() == selectedLuaChonIds?.Count
                && correctIds.All(id => selectedLuaChonIds.Contains(id));

            // <PERSON><PERSON>h điểm
            var score = isCorrect
                ? ctl.CauHoiPhanThiBaiThi?.DiemPhanBo ?? 0
                : 0;

            // Trả về kết quả
            return Task.FromResult(new GradingResult
            {
                Score = score,
                IsCorrect = isCorrect
            });
        }
    }
}
