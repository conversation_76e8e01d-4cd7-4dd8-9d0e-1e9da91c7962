﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace eExamSmart_BE.Models
{
    public class LuaChon : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public Guid CauHoiId { get; set; }
        [ForeignKey(nameof(CauHoiId))]
        public CauHoi? CauHoi { get; set; }

        [MaxLength(500)]
        public string NoiDung { get; set; }

        public bool IsCorrect { get; set; }

        public int ThuTu { get; set; }

    }
}