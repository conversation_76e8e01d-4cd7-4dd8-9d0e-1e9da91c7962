﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface ICauHoiPhanThiBaiThiRepository : IRepository<CauHoiPhanThiBaiThi>
    {

    }
    public class CauHoiPhanThiBaiThiRepository : Repository<CauHoiPhanThiBaiThi>, ICauHoiPhanThiBaiThiRepository
    {
        public CauHoiPhanThiBaiThiRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
