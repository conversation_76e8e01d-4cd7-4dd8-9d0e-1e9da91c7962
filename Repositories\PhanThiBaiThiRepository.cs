﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface IPhanThiBaiThiRepository : IRepository<PhanThiBaiThi>
    {

    }
    public class PhanThiBaiThiRepository : Repository<PhanThiBaiThi>, IPhanThiBaiThiRepository
    {
        public PhanThiBaiThiRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
