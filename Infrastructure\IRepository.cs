﻿using System.Linq.Expressions;

namespace eExamSmart_BE.Infrastructure
{
    public interface IRepository<T> where T : class
    {
        // Thêm 1 entity mới
        Task<T> AddAsync(T entity);

        // Thêm nhiều entity
        Task AddRangeAsync(IEnumerable<T> entities);

        // Cập nhật 1 entity
        Task UpdateAsync(T entity);

        // Cập nhật nhiều entity
        Task UpdateRangeAsync(IEnumerable<T> entities);

        // Xoá 1 entity theo id
        Task DeleteAsync(object id);

        // Xoá nhiều entity theo danh sách id (hoặc object)
        Task DeleteRangeAsync(List<object> lst);

        // Xoá nhiều entity theo danh sách entity
        Task RemoveRangeAsync(IEnumerable<T> entities);

        // Lấy 1 entity theo id
        Task<T?> GetByIdAsync(object id);

        // Lấy 1 entity thỏa mãn điều kiện
        Task<T?> GetSingleAsync(Expression<Func<T, bool>> whereCondition, string[]? includes = null);

        // Lấy tất cả các entity mà không có điều kiện
        Task<ICollection<T>> GetAll_No_ConditionAsync(Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null, string[]? includes = null);

        // Lấy tất cả các entity theo điều kiện
        Task<ICollection<T>> GetAllAsync(Expression<Func<T, bool>> whereCondition, Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null, string[]? includes = null);

        // Lấy danh sách có phân trang, trả về tuple gồm danh sách và tổng số bản ghi
        Task<(ICollection<T> Data, int Total)> GetAllPagingAsync(
            Expression<Func<T, bool>> whereCondition,
            int page,
            int pageSize,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
            string[]? includes = null);

        // Một phiên bản khác của phân trang, trả về tuple gồm: danh sách, tổng số bản ghi và tổng số trang
        Task<(ICollection<T> Data, int TotalRow, int TotalPage)> GetAllPaging1Async(
            int page,
            int pageSize,
            Expression<Func<T, bool>>? whereCondition = null,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
            string[]? includes = null);

        // Đếm số lượng entity theo điều kiện
        Task<int> CountAsync(Expression<Func<T, bool>> whereCondition);

        // Kiểm tra sự tồn tại của entity theo điều kiện
        Task<bool> ExistsAsync(Expression<Func<T, bool>> whereCondition);

        // Thực thi thủ tục store và trả về danh sách entity
        Task<ICollection<T>> ExecWithStoreProcedureAsync(string query, params object[] parameters);

        // Lấy phần tử đầu tiên hoặc mặc định theo điều kiện
        Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> whereCondition,
                                    Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
                                    string[]? includes = null);

        IQueryable<T> Query(Expression<Func<T, bool>>? filter = null, string[]? includes = null);
    }
}
