﻿using eExamSmart_BE.Services.Interfaces;
using Newtonsoft.Json;
using RabbitMQ.Client;
using System.Text;

namespace eExamSmart_BE.Services.Implementations
{
    public class RabbitMqGradingQueue : IGradingQueue
    {
        private readonly IConnectionFactory _factory;

        public RabbitMqGradingQueue(IConnectionFactory factory)
        {
            _factory = factory;
        }

        public async Task EnqueueBaiThiForGradingAsync(Guid baiThiId)
        {
            await using var connection = await _factory.CreateConnectionAsync();
            await using var channel = await connection.CreateChannelAsync();

            var message = JsonConvert.SerializeObject(new { BaiThiId = baiThiId });
            var body = Encoding.UTF8.GetBytes(message);
            var props = new BasicProperties { Persistent = true };

            // Đảm bảo match với exchange/queue đã declare
            await channel.BasicPublishAsync(
                exchange: "main_exchange",
                routingKey: "grading_queue",
                mandatory: true,
                basicProperties: props,
                body: body
            );
        }
    }
}
