﻿using eExamSmart_BE.Models;
using eExamSmart_BE.Services.Interfaces;

namespace eExamSmart_BE.Services.AnserGrader
{
    public class ShortAnswerGrader : IAnswerGrader
    {
        private readonly IAiGradingService aiGradingService;
        private readonly ILogger<ShortAnswerGrader> logger;
        public ShortAnswerGrader(IAiGradingService aiGradingService, ILogger<ShortAnswerGrader> logger)
        {
            this.aiGradingService = aiGradingService;
            this.logger = logger;
        }

        public bool CanHandle(string loaiCauHoi)
        {
            return loaiCauHoi == "short_answer";
        }

        public async Task<GradingResult> GradeAsync(CauTraLoi ctl, CauHoi cauHoi, CancellationToken cancellationToken)
        {
            var userAnswer = ctl.ShortAnswerText;
            if (string.IsNullOrWhiteSpace(cauHoi.NoiDung) || string.IsNullOrWhiteSpace(userAnswer))
                return new GradingResult { IsCorrect = false, Score = 0 };

            //aiGradingService.GradeShortAnswerAsync(cauHoi.NoiDung, "", userAnswer);
            var result = await aiGradingService.GradeShortAnswerAsync(cauHoi.NoiDung, "", userAnswer, cancellationToken);

            // Tính điểm
            var score = ctl.CauHoiPhanThiBaiThi?.DiemPhanBo * (result.Score / 10) ?? 0;

            logger.LogWarning($"userAnswer: {userAnswer}");
            logger.LogWarning($"Điểm: {result.Score}/10 ~ {score}/{ctl.CauHoiPhanThiBaiThi?.DiemPhanBo} - {result.IsCorrect} - {cauHoi.NoiDung}");

            return new GradingResult
            {
                Score = score,
                IsCorrect = result.IsCorrect
            };
        }
    }
}
