﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Repositories;

namespace eExamSmart_BE.UOW
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly MyDbContext _db;

        public IDeThiRepository DeThis { get; private set; }
        public IPhanThiRepository PhanThis { get; private set; }
        public IPhanCauHoiRepository PhanCauHois { get; private set; }
        public IRandomRuleRepository RandomRules { get; private set; }
        public ICauHoiRepository CauHois { get; private set; }
        public ILuaChonRepository LuaChons { get; private set; }
        public ITagRepository Tags { get; private set; }
        public ILichThiRepository LichThis { get; private set; }
        public IBaiThiRepository BaiThis { get; private set; }
        public IPhanThiBaiThiRepository PhanThiBaiThis { get; private set; }
        public ICauHoiPhanThiBaiThiRepository CauHoiPhanThiBaiThis { get; private set; }
        public ICauTraLoiRepository CauTraLois { get; private set; }
        public ICauHoiTagRepository CauHoiTags { get; private set; }
        public IRandomRuleTagRepository RandomRuleTags { get; private set; }

        public UnitOfWork(MyDbContext db)
        {
            _db = db;

            DeThis = new DeThiRepository(db);
            PhanThis = new PhanThiRepository(db);
            PhanCauHois = new PhanCauHoiRepository(db);
            RandomRules = new RandomRuleRepository(db);
            CauHois = new CauHoiRepository(db);
            LuaChons = new LuaChonRepository(db);
            Tags = new TagRepository(db);
            LichThis = new LichThiRepository(db);
            BaiThis = new BaiThiRepository(db);
            PhanThiBaiThis = new PhanThiBaiThiRepository(db);
            CauHoiPhanThiBaiThis = new CauHoiPhanThiBaiThiRepository(db);
            CauTraLois = new CauTraLoiRepository(db);
            CauHoiTags = new CauHoiTagRepository(db);
            RandomRuleTags = new RandomRuleTagRepository(db);
        }

        public async Task<int> CompleteAsync(CancellationToken cancellationToken = default)
        {
            return await _db.SaveChangesAsync(cancellationToken);
        }

        public void Dispose()
        {
            _db.Dispose();
        }
    }
}
