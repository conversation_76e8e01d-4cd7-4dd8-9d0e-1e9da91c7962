﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace eExamSmart_BE.Models
{
    public class RandomRule : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public Guid PhanThiId { get; set; }
        [ForeignKey(nameof(PhanThiId))]
        public PhanThi? PhanThi { get; set; }

        [MaxLength(50)]
        public string? LoaiCauHoi { get; set; }

        [MaxLength(50)]
        public string? MucDo { get; set; }

        public int SoLuong { get; set; }

        public float Diem { get; set; }

        //Thêm trường mới cho reading comprehension
        public int? SoLuongMenhDe { get; set; }

        public List<RandomRuleTag> RandomRuleTags { get; set; } = new();

    }

}
