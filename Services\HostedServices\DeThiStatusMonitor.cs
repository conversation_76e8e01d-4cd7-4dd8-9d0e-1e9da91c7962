﻿
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models.Enums;

namespace eExamSmart_BE.Services.HostedServices
{
    public class DeThiStatusMonitor : BackgroundService
    {
        private readonly IServiceScopeFactory scopeFactory;
        private readonly ILogger<DeThiStatusMonitor> logger;

        public DeThiStatusMonitor(IServiceScopeFactory scopeFactory, ILogger<DeThiStatusMonitor> logger)
        {
            this.scopeFactory = scopeFactory;
            this.logger = logger;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                using var scope = scopeFactory.CreateScope();
                var uow = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                // Lấy danh sách đề thi đã hết hạn
                var now = DateTime.Now;
                var deThiHetHans = await uow.DeThis.GetAllAsync(
                    dt => !dt.IsDeleted
                    && dt.Status == DeThiStatus.DaDienRa
                    && dt.ThoiGianKetThuc < now
                );

                foreach(var deThi in deThiHetHans)
                {
                    deThi.Status = DeThiStatus.HetHan;
                }

                if (deThiHetHans.Any())
                {
                    await uow.CompleteAsync();
                }

                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }
    }
}
