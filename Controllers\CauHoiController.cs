﻿using eExamSmart_BE.Dtos;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;

namespace eExamSmart_BE.Controllers
{
    [EnableCors("CorsApi")]
    [Authorize]
    [Route("api/EES/[controller]")]
    [ApiController]
    public class CauHoiController : ControllerBase
    {
        private readonly IUnitOfWork uow;
        public static IWebHostEnvironment environment;

        public CauHoiController(IUnitOfWork _uow, IWebHostEnvironment _environment)
        {
            uow = _uow;
            environment = _environment;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllCauHoiWithTags([FromQuery] string? keyword)
        {
            var cauHois = await uow.CauHois.GetAllAsync(
                ch => !ch.IsDeleted && 
                    (string.IsNullOrEmpty(keyword) || ch.NoiDung.ToLower().Contains(keyword)),
                includes: new[] { "CauHoiTags.Tag" },
                orderBy: ch => ch.OrderByDescending(c => c.UpdatedDate ?? c.CreatedDate)
            );
            return Ok(cauHois.Select(ch => new
            {
                ch.Id,
                ch.NoiDung,
                ch.LoaiCauHoi,
                tags = ch.CauHoiTags
                    .Where(ct => !ct.IsDeleted)
                    .OrderBy(ct => ct.Tag?.TenTag)
                    .Select(ct => new
                    {
                        ct.Tag?.Id,
                        ct.Tag?.TenTag
                    }).ToList()
            }));
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetByIdWithTags(Guid id)
        {
            var cauHoi = await uow.CauHois.GetSingleAsync(
                ch => ch.Id == id && !ch.IsDeleted,
                includes: new[] { "CauHoiTags.Tag", "LuaChons" }
            );
            if (cauHoi == null)
            {
                return NotFound();
            }
            return Ok(new
            {
                cauHoi.Id,
                cauHoi.NoiDung,
                cauHoi.LoaiCauHoi,
                tag = cauHoi.CauHoiTags
                    .Where(ct => !ct.IsDeleted)
                    .Select(ct => new
                    {
                        ct.Tag?.Id,
                        ct.Tag?.TenTag
                    }).ToList(),
                cauHoi.MucDo,
                cauHoi.HinhAnhUrl,
                cauHoi.AmThanhUrl,
                cauHoi.DapAnMau,
                LuaChons = cauHoi.LuaChons
                    .Where(lc => !lc.IsDeleted)
                    .OrderBy(lc => lc.ThuTu)
                    .Select(lc => new
                    {
                        lc.Id,
                        lc.NoiDung,
                        lc.ThuTu,
                        lc.IsCorrect
                    }).ToList()
            });
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateCauHoiRequest req)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            // 1. Tạo câu hỏi
            var cauHoi = new CauHoi
            {
                Id = req.Id ?? Guid.NewGuid(),
                NoiDung = req.NoiDung,
                LoaiCauHoi = req.LoaiCauHoi,
                MucDo = req.MucDo,
                HinhAnhUrl = req.HinhAnhUrl,
                AmThanhUrl = req.AmThanhUrl,
                DapAnMau = req.DapAnMau,
            };
            await uow.CauHois.AddAsync(cauHoi);

            // 2. Tạo lựa chọn kèm theo
            foreach (var lc in req.LuaChons)
            {
                var luaChon = new LuaChon
                {
                    Id = Guid.NewGuid(),
                    CauHoiId = cauHoi.Id,
                    NoiDung = lc.NoiDung,
                    IsCorrect = lc.IsCorrect,
                    ThuTu = lc.ThuTu
                };
                await uow.LuaChons.AddAsync(luaChon);
            }

            // 3. Gán TagIds vào bảng CauHoiTag
            foreach (var tagId in req.TagIds.Distinct())
            {
                var cauHoiTag = new CauHoiTag
                {
                    Id = Guid.NewGuid(),
                    CauHoiId = cauHoi.Id,
                    TagId = new Guid(tagId),
                };
                await uow.CauHoiTags.AddAsync(cauHoiTag);
            }

            // 3. Lưu thay đổi
            await uow.CompleteAsync();

            return CreatedAtAction(nameof(GetByIdWithTags), new { id = cauHoi.Id }, new { id = cauHoi.Id });
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] CreateCauHoiRequest req)
        {
            if (!ModelState.IsValid || id != req.Id)
                return BadRequest("ID không hợp lệ.");

            var cauHoi = await uow.CauHois.GetSingleAsync(
                c => c.Id == id && !c.IsDeleted,
                includes: new[] { "LuaChons" }
            );

            if (cauHoi == null)
                return NotFound();

            //1. Cập nhật thông tin câu hỏi 
            cauHoi.NoiDung = req.NoiDung;
            cauHoi.LoaiCauHoi = req.LoaiCauHoi;
            cauHoi.MucDo = req.MucDo;
            cauHoi.HinhAnhUrl = req.HinhAnhUrl;
            cauHoi.AmThanhUrl = req.AmThanhUrl;
            cauHoi.DapAnMau = req.DapAnMau;

            //Đồng bộ LuaChon (xóa cũ - thêm mới - update tồn tại)
            var existingLuaChons = cauHoi.LuaChons.ToList();

            // Xoá các LuaChon không còn trong danh sách
            var removed = existingLuaChons
                .Where(old => !req.LuaChons.Any(newLC => newLC.Id == old.Id))
                .ToList();

            foreach (var lc in removed)
                await uow.LuaChons.DeleteAsync(lc.Id);

            // Thêm hoặc cập nhật lại LuaChon
            foreach (var lcDto in req.LuaChons)
            {
                var existing = existingLuaChons.FirstOrDefault(x => x.Id == lcDto.Id);
                if (existing != null)
                {
                    // Update
                    existing.NoiDung = lcDto.NoiDung;
                    existing.ThuTu = lcDto.ThuTu;
                    existing.IsCorrect = lcDto.IsCorrect;
                }
                else
                {
                    // Thêm mới
                    var newLc = new LuaChon
                    {
                        Id = Guid.NewGuid(),
                        CauHoiId = cauHoi.Id,
                        NoiDung = lcDto.NoiDung,
                        ThuTu = lcDto.ThuTu,
                        IsCorrect = lcDto.IsCorrect
                    };
                    await uow.LuaChons.AddAsync(newLc);
                }
            }

            //Đồng bộ lại CauHoiTag
            var oldTags = await uow.CauHoiTags.GetAllAsync(ct => ct.CauHoiId == cauHoi.Id);
            foreach (var oldTag in oldTags)
            {
                await uow.CauHoiTags.DeleteAsync(oldTag.Id);
            }

            foreach (var tagId in req.TagIds.Distinct())
            {
                var newTag = new CauHoiTag
                {
                    Id = Guid.NewGuid(),
                    CauHoiId = cauHoi.Id,
                    TagId = new Guid(tagId),
                };
                await uow.CauHoiTags.AddAsync(newTag);
            }

            await uow.CompleteAsync();
            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var cauHoi = await uow.CauHois.GetSingleAsync(
                c => c.Id == id && !c.IsDeleted,
                includes: new[] { "LuaChons" }
            );

            if (cauHoi == null)
                return NotFound();

            var isUsedInDeThi = await uow.PhanCauHois.ExistsAsync(pch => pch.CauHoiId == id && !pch.IsDeleted);
            var isUsedInBaiThi = await uow.CauHoiPhanThiBaiThis.ExistsAsync(c => c.CauHoiId == id && !c.IsDeleted);

            if (isUsedInDeThi || isUsedInBaiThi)
                return Conflict("Câu hỏi này đang được sử dụng trong đề thi hoặc bài thi, không thể xóa.");

            // Xoá các LuaChon liên quan
            foreach (var luaChon in cauHoi.LuaChons)
            {
                await uow.LuaChons.DeleteAsync(luaChon.Id);
            }
            // Xoá các Tag liên quan
            var cauHoiTags = await uow.CauHoiTags.GetAllAsync(ct => ct.CauHoiId == id);
            foreach (var tag in cauHoiTags)
            {
                await uow.CauHoiTags.DeleteAsync(tag.Id);
            }

            await uow.CauHois.DeleteAsync(id);
            await uow.CompleteAsync();

            // Xóa file media nếu có
            if (!string.IsNullOrWhiteSpace(cauHoi.HinhAnhUrl))
            {
                var imagePath = Path.Combine(Directory.GetParent(Directory.GetCurrentDirectory())!.FullName, cauHoi.HinhAnhUrl.TrimStart('/'));
                //var imagePath = Path.Combine(environment.ContentRootPath, cauHoi.HinhAnhUrl.TrimStart('/'));
                if (System.IO.File.Exists(imagePath))
                {
                    System.IO.File.Delete(imagePath);
                }
            }

            if (!string.IsNullOrWhiteSpace(cauHoi.AmThanhUrl))
            {
                var audioPath = Path.Combine(Directory.GetParent(Directory.GetCurrentDirectory())!.FullName, cauHoi.AmThanhUrl.TrimStart('/'));
                if (System.IO.File.Exists(audioPath))
                {
                    System.IO.File.Delete(audioPath);
                }
            }

            return NoContent();
        }

        [HttpPost("Read_Excel")]
        public async Task<IActionResult> Read_Excel(IFormFile file)
        {
            // 1. Validate file
            if (file == null || file.Length == 0)
                return BadRequest("Không có file được upload.");
            var ext = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!new[] { ".xls", ".xlsx" }.Contains(ext))
                return BadRequest("Định dạng file không được phép.");

            // 2. Đọc trực tiếp vào MemoryStream
            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);
            stream.Position = 0;

            // 3. EPPlus license
            using var package = new ExcelPackage(stream);

            var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            if (worksheet == null)
                return BadRequest("File không chứa sheet.");

            int rowCount = worksheet.Dimension.End.Row;

            // 4. Lấy toàn bộ tag từ DB để validate
            var allTags = await uow.Tags.GetAllAsync(t => !t.IsDeleted);

            var list = new List<ImportCauHoiDto>();

            // 5. Bắt đầu đọc từ dòng thứ 3 (Excel row 3)
            for (int row = 4; row <= rowCount; row++)
            {
                // Nếu cell câu hỏi trống => dừng
                var rawQuestion = worksheet.Cells[row, 3].GetValue<string>()?.Trim();
                if (string.IsNullOrWhiteSpace(rawQuestion))
                    break;

                var rawTagCodes = worksheet.Cells[row, 2].GetValue<string>();
                var rawCorrect = worksheet.Cells[row, 4].GetValue<string>();

                var dto = new ImportCauHoiDto
                {
                    RowNo = row - 3,
                    NoiDung = rawQuestion,
                    TagCodes = rawTagCodes?
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(t => t.Trim())
                        .ToArray()
                        ?? Array.Empty<string>(),
                    CorrectAnswers = rawCorrect?
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(t => t.Trim().ToUpper())
                        .ToArray()
                        ?? Array.Empty<string>(),
                    Options = new List<string>(),
                    ErrorMessages = new List<string>()
                };

                // 6. Đọc các phương án A…H (cột 5→12)
                for (int col = 5; col <= 12; col++)
                {
                    var opt = worksheet.Cells[row, col].GetValue<string>()?.Trim();
                    if (!string.IsNullOrWhiteSpace(opt))
                        dto.Options.Add(opt);
                }

                // 7. Validate bắt buộc
                if (dto.Options.Count == 0)
                    dto.ErrorMessages.Add("Chưa cung cấp phương án trả lời.");
                if (string.IsNullOrWhiteSpace(dto.NoiDung))
                    dto.ErrorMessages.Add("Nội dung câu hỏi trống.");
                if (dto.CorrectAnswers.Length == 0)
                    dto.ErrorMessages.Add("Chưa đánh dấu đáp án đúng.");
                else
                {
                    foreach (var ans in dto.CorrectAnswers)
                    {
                        if (!"ABCDEFGH".Contains(ans))
                            dto.ErrorMessages.Add($"Đáp án “{ans}” không hợp lệ (chỉ A–H).");
                        else
                        {
                            int idx = ans[0] - 'A';
                            if (idx < 0 || idx >= dto.Options.Count)
                                dto.ErrorMessages.Add($"Đáp án “{ans}” vượt quá số phương án.");
                        }
                    }
                }

                // 8. Validate tag tồn tại
                foreach (var code in dto.TagCodes)
                {
                    if (!allTags.Any(t => t.MaTag.Equals(code, StringComparison.OrdinalIgnoreCase)))
                        dto.ErrorMessages.Add($"Tag “{code}” không tồn tại.");
                }

                dto.HasError = dto.ErrorMessages.Any();
                list.Add(dto);
            }

            // 9. Sắp lỗi lên đầu
            var ordered = list
                .OrderBy(i => i.HasError ? 0 : 1)
                .ToList();

            return Ok(new
            {
                errorCount = ordered.Count(i => i.HasError),
                rowCount = ordered.Count,
                data = ordered
            });
        }

        [HttpPost("Save_Import")]
        public async Task<IActionResult> SaveImport([FromBody]List<ImportCauHoiDto> cauHoiDtos)
        {
            if (cauHoiDtos == null || cauHoiDtos.Count == 0)
                return Conflict("Không có dữ liệu để lưu.");
            // 1. Validate dữ liệu

            // 1. Lấy toàn bộ tag từ DB để validate
            var allTags = await uow.Tags.GetAllAsync(t => !t.IsDeleted);
            var allTagsDict = allTags.ToDictionary(t => t.MaTag.ToUpperInvariant(), t => t.Id);

            foreach (var cauHoi in cauHoiDtos)
            {
                // validate cauHoi.TagCodes có cái nào không có trong DB không
                foreach (var code in cauHoi.TagCodes)
                {
                    if (!allTagsDict.ContainsKey(code.ToUpperInvariant()))
                    {
                        cauHoi.ErrorMessages.Add($"Mã nhãn “{code}” không tồn tại.");
                        cauHoi.HasError = true;
                    }
                }
                if (cauHoi.HasError)
                    return Conflict($"Dòng {cauHoi.RowNo} có lỗi: {string.Join(", ", cauHoi.ErrorMessages)}");

                //Tạo câu hỏi
                var newCauHoi = new CauHoi
                {
                    Id = Guid.NewGuid(),
                    NoiDung = cauHoi.NoiDung,
                    LoaiCauHoi = cauHoi.CorrectAnswers.Length > 1 ? "multiple_choice" : "single_choice",
                    MucDo = null,
                    HinhAnhUrl = null,
                    AmThanhUrl = null
                };
                await uow.CauHois.AddAsync(newCauHoi);

                //Tạo lựa chọn
                for (int i = 0; i < cauHoi.Options.Count; i++)
                {
                    var letter = ((char)('A' + i)).ToString();
                    var luaChon = new LuaChon
                    {
                        Id = Guid.NewGuid(),
                        CauHoiId = newCauHoi.Id,
                        NoiDung = cauHoi.Options[i],
                        IsCorrect = cauHoi.CorrectAnswers.Select(x => x.Trim().ToUpper()).Contains(letter),
                        ThuTu = i + 1
                    };
                    await uow.LuaChons.AddAsync(luaChon);
                }

                //Tạo CauHoiTag
                foreach (var tagCode in cauHoi.TagCodes)
                {
                    var tagId = allTagsDict[tagCode.ToUpperInvariant()];
                    var cauHoiTag = new CauHoiTag
                    {
                        Id = Guid.NewGuid(),
                        CauHoiId = newCauHoi.Id,
                        TagId = tagId
                    };
                    await uow.CauHoiTags.AddAsync(cauHoiTag);
                }
            }

            await uow.CompleteAsync();
            return Ok();
        }

        [HttpGet("export-template")]
        public ActionResult FileMau()
        {
            string fullFilePath = Path.Combine(Directory.GetParent(environment.ContentRootPath)!.FullName, "Uploads", "File_Mau_ImportCauHoi.xlsx");
            string fileName = "File_Mau_ImportCauHoi.xlsx";
            using (ExcelPackage package = new ExcelPackage(new FileInfo(fullFilePath)))
            {
                if (package.Workbook.Worksheets.Count == 0)
                {
                    package.Workbook.Worksheets.Add("Sheet1");
                }

                return Ok(new { data = package.GetAsByteArray(), fileName });
            }
        }

        [HttpPost("upload-media")]
        public async Task<IActionResult> UploadMedia([FromQuery] Guid cauHoiId, [FromQuery] string type, [FromForm] IFormFile file)
        {
            if (file == null || file.Length == 0)
                return BadRequest("File rỗng.");

            if (type != "image" && type != "audio")
                return BadRequest("Loại file không hợp lệ.");

            var folder = Path.Combine(Directory.GetParent(Directory.GetCurrentDirectory())!.FullName, "Uploads", "CauHoi");
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);

            var ext = Path.GetExtension(file.FileName);
            var fileName = $"{cauHoiId}_{type}{ext}";
            var filePath = Path.Combine(folder, fileName);

            // Xóa file cũ nếu có
            if (System.IO.File.Exists(filePath))
                System.IO.File.Delete(filePath);

            using var stream = new FileStream(filePath, FileMode.Create);
            await file.CopyToAsync(stream);

            var relativeUrl = $"/Uploads/CauHoi/{fileName}";
            return Ok(new { url = relativeUrl });
        }
    }
}
