﻿using eExamSmart_BE.Helpers;
using eExamSmart_BE.Models;
using eExamSmart_BE.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace eExamSmart_BE.Services.AnserGrader
{
    public class VoiceAnswerGrader : IAnswerGrader
    {
        private readonly IAiGradingService aiGradingService;
        private readonly WhisperService whisperService;
        private ILogger<VoiceAnswerGrader> logger;
        public VoiceAnswerGrader(IAiGradingService aiGradingService, WhisperService whisperService, ILogger<VoiceAnswerGrader> logger)
        {
            this.aiGradingService = aiGradingService;
            this.whisperService = whisperService;
            this.logger = logger;
        }
        public bool CanHandle(string loaiCauHoi)
        {
            return loaiCauHoi == "voice_record";
        }

        public async Task<GradingResult> GradeAsync(CauTraLoi ctl, CauHoi cauHoi, CancellationToken cancellationToken)
        {
            var projectRootPath = Directory.GetParent(Directory.GetCurrentDirectory())!.FullName;
            var audioPath = Path.Combine(projectRootPath, ctl.AudioUrl.TrimStart('/').Replace("/", Path.DirectorySeparatorChar.ToString()));

            if (string.IsNullOrWhiteSpace(cauHoi.NoiDung) || string.IsNullOrWhiteSpace(ctl.AudioUrl) || !File.Exists(audioPath))
            {
                // Nếu file không tồn tại, trả về kết quả không đúng
                return new GradingResult { IsCorrect = false, Score = 0 };
            }

            // Chuyển đổi âm thanh sang văn bản
            var transcribe = await whisperService.TranscribeAsync(audioPath);

            //Xóa các ký tự không cần thiết
            transcribe = TranscriptHelper.CleanTranscript(transcribe);

            var result = await aiGradingService.GradeVoiceAnswerAsync(cauHoi.NoiDung, cauHoi.DapAnMau, transcribe, cancellationToken);

            // Tính điểm
            var score = ctl.CauHoiPhanThiBaiThi?.DiemPhanBo * (result.Score / 10) ?? 0;

            logger.LogWarning($"transcribe: {transcribe}");
            logger.LogWarning($"Điểm: {result.Score}/10 ~ {score}/{ctl.CauHoiPhanThiBaiThi?.DiemPhanBo} - {result.IsCorrect} - {cauHoi.NoiDung}");

            return new GradingResult
            {
                Score = score,
                IsCorrect = result.IsCorrect,
                AudioText = transcribe
            };
        }
    }
}
