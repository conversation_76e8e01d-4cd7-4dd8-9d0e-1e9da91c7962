﻿using eExamSmart_BE.Models;

namespace eExamSmart_BE.Services.Interfaces
{
    public interface IAnswerGrader
    {
        bool CanHandle(string loaiCauHoi);
        Task<GradingResult> GradeAsync(CauTraLoi ctl, CauHoi cauHoi, CancellationToken cancellationToken);
    }
    public class GradingResult
    {
        public float Score { get; set; }
        public bool IsCorrect { get; set; }
        public string AudioText { get; set; } = string.Empty;
    }
}
