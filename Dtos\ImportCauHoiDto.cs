namespace StaffInsightSolution_BE.Dtos
{
    /// <summary>
    /// DTO kết quả đọc mỗi dòng trong Excel
    /// </summary>
    public class ImportCauHoiDto
    {
        public int RowNo { get; set; }                  // Số dòng Excel
        public string NoiDung { get; set; }             // Nội dung câu hỏi
        public string[] TagCodes { get; set; }          // M<PERSON> nhãn (tách bởi dấu ,)
        public List<string> Options { get; set; }       // Các phương án A…H
        public string[] CorrectAnswers { get; set; }    // Đáp án đúng (ví dụ ["A","C"])
        public bool HasError { get; set; }              // Cờ có lỗi không
        public List<string> ErrorMessages { get; set; } // Danh sách mô tả lỗi
    }
}
