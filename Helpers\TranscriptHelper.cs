﻿using System.Text.RegularExpressions;

namespace eExamSmart_BE.Helpers
{
    public class TranscriptHelper
    {
        public static string CleanTranscript(string transcript)
        {
            // <PERSON>ại bỏ dấu thời gian trong ngoặc vuông
            transcript = Regex.Replace(transcript, @"\[\d{2}:\d{2}:\d{2}\.\d{3}\s*-->\s*\d{2}:\d{2}:\d{2}\.\d{3}\]", string.Empty);
            // Thay thế ký tự newline bằng khoảng trắng
            transcript = transcript.Replace("\r\n", " ").Replace("\n", " ");
            // <PERSON><PERSON><PERSON> bỏ khoảng trắng thừa
            transcript = Regex.Replace(transcript, @"\s+", " ").Trim();
            return transcript;
        }
    }
}
