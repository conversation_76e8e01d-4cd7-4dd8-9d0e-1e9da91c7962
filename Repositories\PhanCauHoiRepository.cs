﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;

namespace eExamSmart_BE.Repositories
{
    public interface IPhanCauHoiRepository : IRepository<PhanCauHoi>
    {

    }
    public class PhanCauHoiRepository : Repository<PhanCauHoi>, IPhanCauHoiRepository
    {
        public PhanCauHoiRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
