﻿using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;
using eExamSmart_BE.Services.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using System.Net.WebSockets;

namespace eExamSmart_BE.Services.Implementations
{
    public class ExamGrader : IExamGrader
    {
        private readonly IUnitOfWork uow;
        private readonly IEnumerable<IAnswerGrader> graders;
        private readonly ILogger<ExamGrader> logger;


        public ExamGrader(IUnitOfWork uow, IEnumerable<IAnswerGrader> graders, ILogger<ExamGrader> logger)
        {
            this.uow = uow;
            this.graders = graders;
            this.logger = logger;
        }
        public async Task GradeExamAsync(Guid baiThiId, CancellationToken cancellationToken = default)
        {
            var baiThi = await uow.BaiThis.GetSingleAsync(
                bt => !bt.IsDeleted && bt.Id == baiThiId,
                includes: new[]
                {
                    "PhanThiBaiThis.CauHoiPhanThiBaiThis.CauHoi.LuaChons",
                    "PhanThiBaiThis.CauHoiPhanThiBaiThis.CauTraLois"
                }
            );
            if (baiThi == null)
            {
                logger.LogWarning("Không tìm thấy bài thi với Id: {Id}", baiThiId);
                return;
            }

            var cauTraLois = baiThi.PhanThiBaiThis
                .SelectMany(ptbt => ptbt.CauHoiPhanThiBaiThis)
                .SelectMany(chpt => chpt.CauTraLois)
                .ToList();
            foreach (var cauTraLoi in cauTraLois)
            {
                var cauHoi = cauTraLoi.CauHoiPhanThiBaiThi?.CauHoi;
                //Tìm grader phù hợp
                var grader = graders.FirstOrDefault(g => g.CanHandle(cauHoi?.LoaiCauHoi));

                if(grader == null)
                {
                    logger.LogWarning($"Không tìm thấy grader cho loại câu hỏi: {cauHoi?.LoaiCauHoi}");
                    continue;

                }

                var result = await grader.GradeAsync(cauTraLoi, cauHoi, cancellationToken);

                if (result == null)
                {
                    logger.LogInformation("Không tìm thấy grader cho loại câu hỏi: {LoaiCauHoi}", cauHoi.LoaiCauHoi);
                    continue;
                }

                cauTraLoi.IsCorrect = result.IsCorrect;
                cauTraLoi.Diem = result.Score;
                cauTraLoi.AudioText = result.AudioText;
                

            }

            // Tính tổng điểm
            baiThi.TotalScore = cauTraLois
                .Where(c => c.Diem.HasValue)
                .Sum(c => c.Diem.Value);

            await uow.CompleteAsync(cancellationToken);
        }


    }
}
