﻿using eExamSmart_BE.Dtos;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace eExamSmart_BE.Controllers
{
    [EnableCors("CorsApi")]
    [Authorize]
    [Route("api/EES/[controller]")]
    [ApiController]
    public class TagController : ControllerBase
    {
        private readonly IUnitOfWork uow;
        public TagController(IUnitOfWork _uow)
        {
            uow = _uow;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllTags(string? keyword = null)
        {
            var tags = await uow.Tags.GetAllAsync(
                    t => !t.IsDeleted && 
                    (string.IsNullOrEmpty(keyword) || t.TenTag.Contains(keyword) || t.MaTag.Contains(keyword)),
                orderBy: q => q.OrderBy(t => t.TenTag)
            );

            return Ok(tags.Select(t => new
            {
                t.Id,
                t.Ma<PERSON>,
                t.<PERSON>,
                t.<PERSON>
            }));
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var tag = await uow.Tags.GetSingleAsync(
                t => t.Id == id && !t.IsDeleted
            );
            if (tag == null)
            {
                return NotFound();
            }
            return Ok(new
            {
                tag.Id,
                tag.MaTag,
                tag.TenTag,
                tag.MoTa
            });
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] TagDto req)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            if (await uow.Tags.ExistsAsync(t => t.MaTag == req.MaTag && !t.IsDeleted))
                return Conflict("Mã nhãn đã tồn tại.");

            // 1. Tạo tag
            var tag = new Tag
            {
                Id = Guid.NewGuid(),
                MaTag = req.MaTag.ToUpper(),
                TenTag = req.TenTag,
                MoTa = req.MoTa,
            };
            await uow.Tags.AddAsync(tag);
            await uow.CompleteAsync();

            return CreatedAtAction(nameof(GetById), new { id = tag.Id }, new { id = tag.Id });
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, [FromBody] TagDto req)
        {
            if (!ModelState.IsValid || id != req.Id)
                return BadRequest("ID không hợp lệ.");

            if (await uow.Tags.ExistsAsync(t => t.Id != req.Id && t.MaTag == req.MaTag && !t.IsDeleted))
                return Conflict("Mã nhãn đã tồn tại.");

            var tag = await uow.Tags.GetSingleAsync(
                c => c.Id == id && !c.IsDeleted
            );

            if (tag == null)
                return NotFound();

            //1. Cập nhật thông tin tag
            tag.MaTag = req.MaTag.ToUpper();
            tag.TenTag = req.TenTag;
            tag.MoTa = req.MoTa;

            await uow.CompleteAsync();
            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var tag = await uow.Tags.GetSingleAsync(
                c => c.Id == id && !c.IsDeleted
            );

            if (tag == null)
                return NotFound();

            //Return nếu tag đã được sử dụng cho randomRule của đề thi
            var isUsedInRandomRule = await uow.RandomRuleTags.ExistsAsync(rft => rft.TagId == id && !rft.IsDeleted);
            if (isUsedInRandomRule)
                return Conflict("Nhãn này đang được sử dụng trong quy tắc ngẫu nhiên của đề thi, không thể xóa.");

            //Xóa CauHoiTag liên quan
            var cauHoiTags = await uow.CauHoiTags.GetAllAsync(c => c.TagId == id && !c.IsDeleted);
            if (cauHoiTags.Any())
            {
                await uow.CauHoiTags.DeleteRangeAsync(cauHoiTags.Select(c => (object)c.Id).ToList());
            }
            //Xóa tag
            await uow.Tags.DeleteAsync(id);

            await uow.CompleteAsync();
            return NoContent();
        }
    }
}
