﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StaffInsightSolution_BE.Models
{
    public class PhanCauHoi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public Guid PhanThiId { get; set; }
        [ForeignKey(nameof(PhanThiId))]
        public PhanThi? PhanThi { get; set; }

        public Guid CauHoiId { get; set; }
        [ForeignKey(nameof(CauHoiId))]
        public CauHoi? CauHoi { get; set; }

        public int ThuTu { get; set; }

        [Range(0.01, float.MaxValue, ErrorMessage = "Điểm phải lớn hơn 0.")]
        public float Diem { get; set; }
    }
}