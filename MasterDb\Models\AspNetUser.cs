﻿namespace eExamSmart_BE.MasterDB.Models
{
    public class AspNetUser
    {
        public Guid Id { get; set; }
        public string? <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? HinhAnhUrl_Thilogi { get; set; }
        public string? PhoneNumber { get; set; }
        public bool IsDeleted { get; set; }
    }
}