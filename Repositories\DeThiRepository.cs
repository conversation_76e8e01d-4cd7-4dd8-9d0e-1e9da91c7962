﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;
using System.Linq.Expressions;

namespace eExamSmart_BE.Repositories
{
    public interface IDeThiRepository : IRepository<DeThi>
    {

    }
    public class DeThiRepository : Repository<DeThi>, IDeThiRepository
    {
        public DeThiRepository(MyDbContext _db) : base(_db)
        {
        }
        public MyDbContext MyDbContext
        {
            get
            {
                return _db;
            }
        }
    }
}
