﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace StaffInsightSolution_BE.Models
{
    public class PhanThi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public Guid DeThiId { get; set; }
        [ForeignKey(nameof(DeThiId))]
        public DeThi? DeThi { get; set; }

        [MaxLength(1000)]
        public string TenPhanThi { get; set; }

        public int ThuTu { get; set; }

        [MaxLength(50)]
        public string? KyNang { get; set; }

        public ICollection<PhanCauHoi> PhanCauHois { get; set; } = new List<PhanCauHoi>();
        public ICollection<RandomRule> RandomRules { get; set; } = new List<RandomRule>();
        public ICollection<PhanThiBaiThi> PhanThiBaiThis { get; set; } = new List<PhanThiBaiThi>();
    }
}