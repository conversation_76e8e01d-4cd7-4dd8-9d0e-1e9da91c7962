﻿using eExamSmart_BE.Services.Interfaces;

namespace eExamSmart_BE.Services.IAIGradingSevice
{
    public class OpenAiGradingService : IAiGradingService
    {
        public Task<GradingResult> GradeShortAnswerAsync(string questionText, string correctAnswer, string studentAnswer, CancellationToken ct = default)
        {
            throw new NotImplementedException();
        }

        public Task<GradingResult> GradeVoiceAnswerAsync(string questionText, string correctAnswer, string userAnswer, CancellationToken ct = default)
        {
            throw new NotImplementedException();
        }
    }
}
