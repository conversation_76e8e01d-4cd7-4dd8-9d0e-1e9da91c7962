using Newtonsoft.Json;
using eExamSmart_BE.Dtos;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;
using eExamSmart_BE.Models.Enums;
using eExamSmart_BE.Services.Interfaces;

namespace eExamSmart_BE.Services.Implementations
{
    public class ExamService : IExamService
    {
        private readonly IUnitOfWork _uow;
        private readonly IGradingQueue _gradingQueue;

        public ExamService(IUnitOfWork uow, IGradingQueue gradingQueue)
        {
            _uow = uow;
            _gradingQueue = gradingQueue;
        }

        public async Task<Guid> CreateBaiThiAsync(CreateBaiThiRequest request)
        {
            var now = DateTime.Now;
            var deThi = await _uow.DeThis.GetSingleAsync(
                dt => !dt.IsDeleted && dt.Id == request.DeThiId && dt.Status == DeThiStatus.DaDienRa);

            if (deThi == null)
                throw new InvalidOperationException("Không tìm thấy đề thi.");

            if (now < deThi.ThoiGianBatDau)
                throw new InvalidOperationException("Chưa đến giờ thi.");

            if (now > deThi.ThoiGianKetThuc)
                throw new InvalidOperationException("Thời gian thi đã kết thúc.");

            var existing = await _uow.BaiThis.GetSingleAsync(
                bt => !bt.IsDeleted && bt.DeThiId == request.DeThiId && bt.UserId == request.UserId);

            if (existing != null)
                return existing.Id;

            var deThiTemplate = await _uow.DeThis.GetSingleAsync(
                d => d.Id == request.DeThiId && !d.IsDeleted,
                includes: new[] { "PhanThis.PhanCauHois.CauHoi", "PhanThis.RandomRules.RandomRuleTags" });

            if (deThiTemplate == null)
                throw new InvalidOperationException("Đề thi không tồn tại.");

            var totalStatic = deThiTemplate.PhanThis
                .SelectMany(pt => pt.PhanCauHois)
                .Sum(pct => pct.Diem);
            var totalRandom = deThiTemplate.PhanThis
                .SelectMany(pt => pt.RandomRules)
                .Sum(rule => rule.Diem * rule.SoLuong);
            var maxScore = totalStatic + totalRandom;

            var baiThi = new BaiThi
            {
                Id = Guid.NewGuid(),
                DeThiId = request.DeThiId,
                UserId = request.UserId,
                ThoiGianBatDau = DateTime.Now,
                MaxScore = maxScore,
                Duration = deThiTemplate.Duration
            };
            await _uow.BaiThis.AddAsync(baiThi);

            foreach (var phanThi in deThiTemplate.PhanThis.OrderBy(pt => pt.ThuTu))
            {
                var phanThiBaiThi = new PhanThiBaiThi
                {
                    Id = Guid.NewGuid(),
                    BaiThiId = baiThi.Id,
                    PhanThiId = phanThi.Id,
                    TenPhanThiBaiThi = phanThi.TenPhanThi,
                    ThuTu = phanThi.ThuTu
                };
                await _uow.PhanThiBaiThis.AddAsync(phanThiBaiThi);

                var cauHoiCounter = 0;
                foreach (var phanCauHoi in phanThi.PhanCauHois.OrderBy(pch => pch.ThuTu))
                {
                    cauHoiCounter++;
                    var cauHoiBaiThi = new CauHoiPhanThiBaiThi
                    {
                        Id = Guid.NewGuid(),
                        PhanThiBaiThiId = phanThiBaiThi.Id,
                        CauHoiId = phanCauHoi.CauHoiId,
                        DiemPhanBo = phanCauHoi.Diem,
                        ThuTu = phanCauHoi.ThuTu,
                        Nguon = "static"
                    };
                    await _uow.CauHoiPhanThiBaiThis.AddAsync(cauHoiBaiThi);
                }

                foreach (var rule in phanThi.RandomRules)
                {
                    var requiredTagIds = rule.RandomRuleTags
                        .Where(rt => !rt.IsDeleted)
                        .Select(rt => rt.TagId);

                    var candidates = await _uow.CauHois.GetAllAsync(
                            c => !c.IsDeleted && (rule.LoaiCauHoi == null || c.LoaiCauHoi == rule.LoaiCauHoi),
                        includes: new[] { "LuaChons", "CauHoiTags" });

                    candidates = candidates
                        .Where(c => requiredTagIds.All(tagId => c.CauHoiTags.Any(ct => !ct.IsDeleted && ct.TagId == tagId)))
                        .ToList();

                    var cauHoiRandoms = candidates
                        .OrderBy(_ => Guid.NewGuid())
                        .Take(rule.SoLuong)
                        .ToList();

                    foreach (var cauHoi in cauHoiRandoms)
                    {
                        cauHoiCounter++;
                        var menhDesJson = string.Empty;
                        if (cauHoi.LoaiCauHoi == "true_false" && rule.SoLuongMenhDe > 0)
                        {
                            var menhDeList = cauHoi.LuaChons
                                .Where(md => !md.IsDeleted)
                                .OrderBy(_ => Guid.NewGuid())
                                .Take(rule.SoLuongMenhDe.Value)
                                .Select(md => md.Id)
                                .ToList();

                            menhDesJson = Commons.ConvertObjectToJson(menhDeList);
                        }

                        var cauHoiBaiThi = new CauHoiPhanThiBaiThi
                        {
                            Id = Guid.NewGuid(),
                            PhanThiBaiThiId = phanThiBaiThi.Id,
                            CauHoiId = cauHoi.Id,
                            DiemPhanBo = rule.Diem,
                            ThuTu = cauHoiCounter,
                            MenhDeIds = menhDesJson,
                            Nguon = "randomRule",
                        };
                        await _uow.CauHoiPhanThiBaiThis.AddAsync(cauHoiBaiThi);
                    }
                }
            }

            await _uow.CompleteAsync();
            return baiThi.Id;
        }

        public async Task NopBaiThiAsync(SubmitExamRequest req)
        {
            var answers = JsonConvert.DeserializeObject<List<AnswerDto>>(req.Answers ?? "[]");

            var audioMap = new Dictionary<string, string>();
            if (req.AudioFiles != null && req.AudioFiles.Any())
            {
                foreach (var file in req.AudioFiles)
                {
                    if (file.Length > 0)
                    {
                        var uploadRoot = Path.Combine(Directory.GetParent(Directory.GetCurrentDirectory())!.FullName, "Uploads");
                        var fileName = Path.GetFileName(file.FileName);
                        var savePath = Path.Combine(uploadRoot, fileName);
                        using var stream = System.IO.File.Create(savePath);
                        await file.CopyToAsync(stream);
                        var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                        audioMap[nameWithoutExt] = $"/Uploads/{fileName}";
                    }
                }
            }

            var cautraLois = new List<CauTraLoi>();
            foreach (var ans in answers)
            {
                var ctl = new CauTraLoi
                {
                    Id = Guid.NewGuid(),
                    CauHoiPhanThiBaiThiId = ans.CauHoiPhanThiBaiThiId,
                    SelectedLuaChonIds = ans.LoaiCauHoi.Contains("choice") ? Commons.ConvertObjectToJson(ans.YourAnswer) : null,
                    ShortAnswerText = ans.LoaiCauHoi.Contains("short_answer") ? (string)ans.YourAnswer : null,
                    OrderingAnswer = ans.LoaiCauHoi.Contains("ordering") ? Commons.ConvertObjectToJson(ans.YourAnswer) : null,
                    BooleanAnswersJson = ans.LoaiCauHoi.Contains("true_false") ? Commons.ConvertObjectToJson(ans.YourAnswer) : null,
                    AudioUrl = null,
                    AudioText = null,
                    IsCorrect = null,
                    Diem = null,
                    ThoiGian = DateTime.Now
                };
                cautraLois.Add(ctl);
            }

            foreach (var key in audioMap.Keys)
            {
                string[] parts = key.Split('_');
                if (parts.Length == 2 && Guid.TryParse(parts[0], out Guid _) && Guid.TryParse(parts[1], out Guid cauHoiPhanThiBaiThiId))
                {
                    var ctl = new CauTraLoi
                    {
                        Id = Guid.NewGuid(),
                        CauHoiPhanThiBaiThiId = cauHoiPhanThiBaiThiId,
                        AudioUrl = audioMap[key],
                        ThoiGian = DateTime.Now
                    };
                    cautraLois.Add(ctl);
                }
            }

            await _uow.CauTraLois.AddRangeAsync(cautraLois);
            var baiThi = await _uow.BaiThis.GetSingleAsync(bt => bt.Id == req.BaiThiId && !bt.IsDeleted);
            baiThi.ThoiGianKetThuc = DateTime.Now;
            await _uow.CompleteAsync();

            await _gradingQueue.EnqueueBaiThiForGradingAsync(baiThi.Id);
        }
    }
}
