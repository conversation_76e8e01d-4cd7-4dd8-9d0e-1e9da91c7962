﻿using eExamSmart_BE.Models;
using eExamSmart_BE.Services.Interfaces;
using eExamSmart_BE.UOW;
using Newtonsoft.Json;

namespace eExamSmart_BE.Services.AnserGrader
{
    public class ReadingComprehensionGrader : IAnswerGrader
    {
        public bool CanHandle(string loaiCauHoi)
        {
            return loaiCauHoi == "true_false";
        }

        public Task<GradingResult> GradeAsync(CauTraLoi ctl, CauHoi cauHoi, CancellationToken cancellationToken)
        {
            // 1. Deserialize danh sách các Id mệnh đề từ MenhDeIds đã được random
            var menhDeIdList = JsonConvert.DeserializeObject<List<Guid>>(ctl.CauHoiPhanThiBaiThi?.MenhDeIds ?? "[]");
            if (menhDeIdList == null || menhDeIdList.Count == 0)
                return Task.FromResult(new GradingResult { IsCorrect = false, Score = 0 });

            // 2. L<PERSON>y câu trả lời của thí sinh
            var readingAnswers = JsonConvert.DeserializeObject<Dictionary<Guid, bool>>(ctl.BooleanAnswersJson ?? "{}");

            // 3. Lấy thông tin đúng/sai của từng mệnh đề
            var menhDeGoc = cauHoi.LuaChons
                .Where(lc => !lc.IsDeleted && menhDeIdList.Contains(lc.Id))
                .ToDictionary(lc => lc.Id, lc => lc.IsCorrect);

            var maxScore = ctl.CauHoiPhanThiBaiThi?.DiemPhanBo ?? 0;
            var total = menhDeGoc.Count;
            var totalCorrect = 0;

            foreach (var menhDe in menhDeGoc)
            {
                // Nếu thí sinh không chọn T/F → coi như sai
                if (readingAnswers.TryGetValue(menhDe.Key, out var userAnswer))
                {
                    if (userAnswer == menhDe.Value)
                        totalCorrect++;
                }
            }

            var scorePerCorrectAnswer = total > 0 ? (decimal)maxScore / total : 0;
            var score = (float)Math.Round(scorePerCorrectAnswer * totalCorrect, 2);
            var isCorrect = totalCorrect == total;

            return Task.FromResult(new GradingResult
            {
                Score = score,
                IsCorrect = isCorrect
            });
        }
    }
}
