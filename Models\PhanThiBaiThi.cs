﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace StaffInsightSolution_BE.Models
{
    public class PhanThiBaiThi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public Guid BaiThiId { get; set; }
        [ForeignKey(nameof(BaiThiId))]
        public BaiThi? BaiThi { get; set; }

        public Guid PhanThiId { get; set; }
        [ForeignKey(nameof(PhanThiId))]
        public PhanThi? PhanThi { get; set; }

        [MaxLength(1000)]
        public string TenPhanThiBaiThi { get; set; }

        public int ThuTu { get; set; }

        public ICollection<CauHoiPhanThiBaiThi> CauHoiPhanThiBaiThis { get; set; } = new List<CauHoiPhanThiBaiThi>();
    }
}
