﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace StaffInsightSolution_BE.Models
{
    public class Tag : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }
        [MaxLength(100)]
        public string MaTag { get; set; } = string.Empty;

        [MaxLength(100)]
        public string TenTag { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? MoTa { get; set; }

        public ICollection<CauHoiTag> CauHoiTags { get; set; } = new List<CauHoiTag>();
        public ICollection<RandomRule> RandomRules { get; set; } = new List<RandomRule>();
    }
}
