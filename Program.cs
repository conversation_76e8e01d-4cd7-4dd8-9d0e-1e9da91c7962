﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Filters;
using eExamSmart_BE.Helpers;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.MasterDB;
using eExamSmart_BE.Services;
using eExamSmart_BE.Services.AnserGrader;
using eExamSmart_BE.Services.HostedServices;
using eExamSmart_BE.Services.IAIGradingSevice;
using eExamSmart_BE.Services.Implementations;
using eExamSmart_BE.Services.Interfaces;
using eExamSmart_BE.UOW;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using RabbitMQ.Client;
using System.Text;


namespace eExamSmart_BE
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // C<PERSON>u hình các dịch vụ (services)
            builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
            builder.Services.AddSingleton(builder.Configuration);
            builder.Services.AddHttpClient("MyHttpClient");
            builder.Services.AddHttpContextAccessor();

            // Cấu hình từ appsettings.json (đường dẫn whisper, models, v.v.)
            string whisperExePath = builder.Configuration["Whisper:ExecutablePath"];
            string modelsPath = builder.Configuration["Whisper:ModelsPath"];
            builder.Services.AddSingleton(new WhisperService(whisperExePath, modelsPath));

            // Add service
            builder.Services.AddTransient<IUnitOfWork, UnitOfWork>();
            builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
            builder.Services.AddScoped<IUserService, UserService>();
            builder.Services.AddScoped<IExamService, ExamService>();
            builder.Services.AddScoped<IDeThiService, DeThiService>();

            // Add LoaiCauHoi
            builder.Services.AddScoped<IAnswerGrader, SingleChoiceGrader>();
            builder.Services.AddScoped<IAnswerGrader, MultiChoiceGrader>();
            builder.Services.AddScoped<IAnswerGrader, ShortAnswerGrader>();
            builder.Services.AddScoped<IAnswerGrader, VoiceAnswerGrader>();
            builder.Services.AddScoped<IAnswerGrader, OrderingGrader>();
            builder.Services.AddScoped<IAnswerGrader, ReadingComprehensionGrader>();

            // Add Grader & AI service
            builder.Services.AddScoped<IExamGrader, ExamGrader>();
            builder.Services.AddScoped<IAiGradingService, OllamaAiGradingService>();
            //builder.Services.AddScoped<IAiGradingService, OpenAiGradingService>();

            // Add DbContext
            builder.Services.AddDbContext<MyDbContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));
            builder.Services.AddDbContext<MasterDbContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("MasterDbConnection")));

            //Add RabbitMQ ConnectionFactory
            builder.Services.AddSingleton<IConnectionFactory>(sp =>
            {
                var cfg = builder.Configuration.GetSection("RabbitMQ"); ;
                var factory = new ConnectionFactory
                {
                    HostName = cfg["HostName"],
                    Port = int.Parse(cfg["Port"]),
                    UserName = cfg["UserName"],
                    Password = cfg["Password"],
                    VirtualHost = cfg["VirtualHost"]
                };
                return factory;
            });

            // Service queue & initializer
            builder.Services.AddSingleton<IGradingQueue, RabbitMqGradingQueue>();
            builder.Services.AddHostedService<RabbitMqInitializer>();

            //HostedService chính để consume & grading
            builder.Services.AddHostedService<ChamBaiThiWorker>();
            builder.Services.AddHostedService<DeThiStatusMonitor>();


            builder.Services.AddCors(options =>
            {
                options.AddPolicy("CorsApi", policyBuilder =>
                {
                    policyBuilder.AllowAnyHeader()
                                 .AllowAnyMethod()
                                 .SetIsOriginAllowed(host => true)
                                 .AllowCredentials();
                });
            });

            // Cấu hình các thông số strongly typed từ file cấu hình (appsettings.json)
            var appSettingsSection = builder.Configuration.GetSection("AppSettings");
            builder.Services.Configure<AppSettings>(appSettingsSection);

            var appSettings = appSettingsSection.Get<AppSettings>();
            var key = Encoding.ASCII.GetBytes(appSettings.Secret);

            // Cấu hình JWT Authentication
            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };
                options.Events = new JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        var accessToken = context.Request.Query["access_token"];
                        var path = context.HttpContext.Request.Path;
                        if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/dashboard"))
                        {
                            context.Token = accessToken;
                        }
                        return Task.CompletedTask;
                    }
                };
            });

            // Cấu hình Controller, JSON và MVC
            builder.Services.AddControllersWithViews(options =>
            {
                options.Filters.Add(typeof(ApiKeyActionFilter));
            });
            builder.Services.AddControllers().AddNewtonsoftJson(options => options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore);
            builder.Services.AddMvc().AddControllersAsServices();

            // Cấu hình Swagger với security definitions
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "My API", Version = "v1" });
                c.AddSecurityDefinition("bearer", new OpenApiSecurityScheme
                {
                    Description = "Put **_ONLY_** your JWT Bearer token on textbox below!",
                    Type = SecuritySchemeType.Http,
                    BearerFormat = "JWT",
                    Name = "JWT Authentication",
                    In = ParameterLocation.Header,
                    Scheme = "bearer"
                });
                c.AddSecurityDefinition("ApiKey", new OpenApiSecurityScheme
                {
                    Description = "Enter your Api Key below:",
                    Name = "ApiKey",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey
                });
                c.OperationFilter<AuthHeaderOperationFilter>();
            });

            // Xây dựng ứng dụng
            var app = builder.Build();

            // Cấu hình middleware Swagger
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "My API V1");
                //c.RoutePrefix = string.Empty;
                c.RoutePrefix = "swagger";
            });

            // Cấu hình môi trường
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseStatusCodePages();
            }

            app.UseHttpsRedirection();
            app.UseRouting();
            app.UseCors("CorsApi");

            // Cấu hình middleware xử lý ngoại lệ
            app.UseExceptionHandler(appBuilder =>
            {
                appBuilder.Use(async (context, next) =>
                {
                    var error = context.Features[typeof(IExceptionHandlerFeature)] as IExceptionHandlerFeature;

                    if (error != null && error.Error is SecurityTokenExpiredException)
                    {
                        context.Response.StatusCode = 401;
                        context.Response.ContentType = "application/json";

                        await context.Response.WriteAsync(JsonConvert.SerializeObject(new
                        {
                            State = "Unauthorized",
                            Msg = "token expired",
                            detailMsg = error?.Error?.InnerException?.Message,
                        }));
                    }
                    else if (error != null && error.Error != null)
                    {
                        context.Response.StatusCode = 500;
                        context.Response.ContentType = "application/json";
                        await context.Response.WriteAsync(JsonConvert.SerializeObject(new
                        {
                            State = "Internal Server Error",
                            Msg = error.Error.Message,
                            detailMsg = error?.Error?.InnerException?.Message,
                        }));
                    }
                    else
                    {
                        await next();
                    }
                });
            });

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseStaticFiles(new StaticFileOptions
            {
                FileProvider = new PhysicalFileProvider(
                    Path.Combine(Directory.GetParent(Directory.GetCurrentDirectory()).FullName, "Uploads")),
                RequestPath = "/Uploads",
                OnPrepareResponse = ctx =>
                {
                    if (ctx.Context.Request.Path.StartsWithSegments("/Uploads"))
                    {
                        ctx.Context.Response.Headers.Add("Cache-Control", "no-store");
                    }
                }
            });

            // Định nghĩa endpoints
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            // Nếu cần seed data Identity, bạn có thể gọi:
            // await MyIdentityDataInitializer.SeedData(userManager, roleManager);

            // Chạy ứng dụng
            app.Run();
        }
    }
}
