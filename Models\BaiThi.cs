﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace eExamSmart_BE.Models
{
    public class BaiThi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public Guid DeThiId { get; set; }
        [ForeignKey(nameof(DeThiId))]
        public DeThi DeThi { get; set; }

        public Guid? UserId { get; set; }

        public int? Duration { get; set; }
        public DateTime ThoiGianBatDau { get; set; }

        public DateTime? ThoiGianKetThuc { get; set; }

        public float MaxScore { get; set; }

        public float? TotalScore { get; set; }

        public ICollection<PhanThiBaiThi> PhanThiBaiThis { get; set; } = new List<PhanThiBaiThi>();
    }
}
