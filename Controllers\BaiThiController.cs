﻿using eExamSmart_BE.Dtos;
using eExamSmart_BE.Infrastructure;
using eExamSmart_BE.Models;
using eExamSmart_BE.Models.Enums;
using eExamSmart_BE.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;

namespace eExamSmart_BE.Controllers
{
    [EnableCors("CorsApi")]
    [Authorize]
    [Route("api/EES/[controller]")]
    [ApiController]
    public class BaiThiController : ControllerBase
    {
        private readonly IUnitOfWork uow;
        private readonly IUserService _userService;
        private readonly IExamService _examService;

        public BaiThiController(IUnitOfWork _uow, IExamService examService, IUserService userService)
        {
            uow = _uow;
            _examService = examService;
            _userService = userService;
        }

        // GET: api/EES/BaiThi
        /// <summary>
        /// L<PERSON>y danh sách lịch sử bài làm theo người dùng
        /// </summary>
        [HttpGet("nguoi-dung/{userId}/da-nop")]
        public async Task<IActionResult> GetBaiThisByUser(Guid userId)
        {
            // Lấy tất cả đề thi chưa xóa, kèm luôn PhanThis, PhanCauHoi, RandomRule
            var deThis = await uow.DeThis.GetAllAsync(
                d => !d.IsDeleted
                && d.Status == DeThiStatus.DaDienRa
                && d.BaiThis.Any(bt => bt.UserId == userId && bt.ThoiGianKetThuc != null),
                //&& now <= d.ThoiGianKetThuc,
                orderBy: q => q.OrderBy(d => d.TenDeThi),
                includes: new[] { "PhanThis.PhanCauHois", "PhanThis.RandomRules" }
            );

            var baiThis = await uow.BaiThis.GetAllAsync(
                bt => !bt.IsDeleted
                && bt.UserId == userId
                && bt.ThoiGianKetThuc != null
                && bt.DeThi.Status != DeThiStatus.DangSoan,
                orderBy: q => q.OrderByDescending(d => d.ThoiGianKetThuc),
                includes: new[] { "DeThi", "PhanThiBaiThis.CauHoiPhanThiBaiThis.CauHoi" }
            );

            var result = baiThis.Select(bt => new
            {
                bt.Id,
                bt.DeThi.MaDeThi,
                bt.DeThi.TenDeThi,
                bt.DeThi.MoTa,
                Duration = bt.ThoiGianKetThuc.HasValue
                            ? Math.Round((bt.ThoiGianKetThuc.Value - bt.ThoiGianBatDau).TotalMinutes, 1) + " phút"
                            : "-:-:-",
                ThoiGianBatDau = bt.ThoiGianBatDau.ToString("HH:mm - dd'/'MM'/'yyyy"),
                ThoiGianKetThuc = bt.ThoiGianKetThuc?.ToString("HH:mm - dd'/'MM'/'yyyy"),
                bt.MaxScore,
                bt.TotalScore,
                // Tính tổng câu hỏi ngay trên object đã include
                TotalQuestions = bt.PhanThiBaiThis.SelectMany(pt => pt.CauHoiPhanThiBaiThis).Count(),
                CanViewDetail = bt.DeThi.Status == DeThiStatus.HetHan,
            });

            return Ok(result);
        }

        // GET: api/EES/BaiThi
        /// <summary>
        /// Lấy danh sách DeThi kèm thời gian thi và tổng số câu hỏi (không dùng DTO)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            // Lấy tất cả đề thi chưa xóa, kèm luôn PhanThis, PhanCauHoi, RandomRule
            var BaiThis = await uow.BaiThis.GetAllAsync(
                d => !d.IsDeleted
            );

            return Ok(BaiThis);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var baiThi = await uow.BaiThis.GetSingleAsync(
                d => d.Id == id && !d.IsDeleted,
                includes: new[]
                {
                    "DeThi",
                    "PhanThiBaiThis.CauHoiPhanThiBaiThis.CauHoi.LuaChons"
                }
            );

            if (baiThi == null)
            {
                return NotFound();
            }

            // Sử dụng lại CreateExamDto làm response
            var dto = new BaiThiDto
            {
                Id = id,
                DeThiId = baiThi.DeThiId,
                UserId = baiThi.UserId,
                Duration = baiThi.Duration,
                ThoiGianBatDau = baiThi.ThoiGianBatDau,
                ThoiGianKetThuc = baiThi.ThoiGianKetThuc,
                MaxScore = baiThi.MaxScore,
                PhanThiBaiThis = baiThi.PhanThiBaiThis
                    .Where(pt => !pt.IsDeleted)
                    .OrderBy(pt => pt.ThuTu)
                    .Select(pt => new PhanThiBaiThiDto
                    {
                        Id = pt.Id,
                        TenPhanThiBaiThi = pt.TenPhanThiBaiThi,
                        ThuTu = pt.ThuTu,
                        CauHoiPhanThiBaiThis = pt.CauHoiPhanThiBaiThis
                            .Where(chptbt => !chptbt.IsDeleted)
                            .OrderBy(chptbt => chptbt.ThuTu)
                            .Select(chptbt => new CauHoiPhanThiBaiThiDto
                            {
                                Id = chptbt.Id,
                                CauHoiId = chptbt.CauHoiId,
                                ThuTu = chptbt.ThuTu,
                                DiemPhanBo = (int)chptbt.DiemPhanBo,
                                NoiDung = chptbt.CauHoi.NoiDung,
                                LoaiCauHoi = chptbt.CauHoi.LoaiCauHoi,
                                HinhAnhUrl = chptbt.CauHoi.HinhAnhUrl,
                                AmThanhUrl = chptbt.CauHoi.AmThanhUrl,
                                LuaChons = chptbt.CauHoi.LuaChons
                                    .Where(pt => !pt.IsDeleted)
                                    .OrderBy(lc => lc.ThuTu)
                                    .Select(lc => new LuaChonBaiThiDto
                                    {
                                        Id = lc.Id,
                                        NoiDung = lc.NoiDung,
                                        ThuTu = lc.ThuTu
                                    })
                                    .ToList()
                            })
                            .ToList()
                    })
                    .ToList()
            };


            return Ok(dto);
        }

        //Lấy danh sách bài thi đã nộp theo đề thi
        [HttpGet("de-thi/{deThiId}/bai-thi")]
        public async Task<IActionResult> GetSubmittedBaiThisByDeThi(Guid deThiId, string? keyword, bool isNopBai = true, bool isTaiFile = false)
        {
            keyword = keyword?.ToLower();
            var deThi = await uow.DeThis.GetSingleAsync(dt => dt.Id == deThiId && !dt.IsDeleted);
            var baiThis = await uow.BaiThis.GetAllAsync(
                bt => bt.DeThiId == deThiId
                && (isNopBai ? bt.ThoiGianKetThuc != null : bt.ThoiGianKetThuc == null)
                && !bt.IsDeleted,
                includes: new[]
                {
                    "PhanThiBaiThis.CauHoiPhanThiBaiThis.CauTraLois"
                }
            );

            if (baiThis == null)
            {
                return NotFound();
            }

            var users = await _userService.GetAllAsync();

            var result = baiThis.Select(bt =>
            {
                var user = users.FirstOrDefault(u => u.Id == bt.UserId && !u.IsDeleted);
                var allQuestions = bt.PhanThiBaiThis.SelectMany(pt => pt.CauHoiPhanThiBaiThis);

                return new
                {
                    bt.Id,
                    deThi?.MaDeThi,
                    deThi?.TenDeThi,
                    deThi?.MoTa,
                    Duration = bt.ThoiGianKetThuc.HasValue
                            ? Math.Round((bt.ThoiGianKetThuc.Value - bt.ThoiGianBatDau).TotalMinutes, 1) + " phút"
                            : "-:-:-",
                    ThoiGianBatDau = bt.ThoiGianBatDau.ToString("HH:mm - dd'/'MM'/'yyyy"),
                    ThoiGianKetThuc = bt.ThoiGianKetThuc?.ToString("HH:mm - dd'/'MM'/'yyyy"),
                    bt.MaxScore,
                    bt.TotalScore,
                    // Tính tổng câu hỏi ngay trên object đã include
                    TotalQuestions = allQuestions.Count(),
                    //Tổng câu trả lời đúng
                    TotalCorrect = allQuestions.SelectMany(ch => ch.CauTraLois).Count(ctl => ctl.IsCorrect == true),
                    //Tổng câu trả lời sai
                    TotalWrong = allQuestions.SelectMany(ch => ch.CauTraLois).Count(ctl => ctl.IsCorrect == false),
                    //Tổng câu trả lời chưa làm
                    TotalUnanswered = allQuestions.Count(ch => !ch.CauTraLois.Any()),
                    user?.MaNhanVien,
                    user?.FullName,
                    user?.Email,
                };
            });

            //Lọc lại theo keyword
            if (!string.IsNullOrEmpty(keyword))
            {
                result = result.Where(bt =>
                    bt.MaNhanVien?.Contains(keyword, StringComparison.OrdinalIgnoreCase) == true ||
                    bt.FullName?.Contains(keyword, StringComparison.OrdinalIgnoreCase) == true ||
                    bt.Email?.Contains(keyword, StringComparison.OrdinalIgnoreCase) == true);
            }

            if (isTaiFile)
            {
                using ExcelPackage package = new ExcelPackage();
                ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("DanhSachBaiThi");

                // Header
                worksheet.Cells[1, 1].Value = "STT";
                worksheet.Cells[1, 2].Value = "Mã đề thi";
                worksheet.Cells[1, 3].Value = "Tên đề thi";
                worksheet.Cells[1, 4].Value = "Mô tả";
                worksheet.Cells[1, 5].Value = "Mã nhân viên";
                worksheet.Cells[1, 6].Value = "Tên nhân viên";
                worksheet.Cells[1, 7].Value = "Email";
                worksheet.Cells[1, 8].Value = "Bắt đầu";
                worksheet.Cells[1, 9].Value = "Kết thúc";
                worksheet.Cells[1, 10].Value = "Thời gian làm";
                worksheet.Cells[1, 11].Value = "Tổng câu";
                worksheet.Cells[1, 12].Value = "Đúng";
                worksheet.Cells[1, 13].Value = "Sai";
                worksheet.Cells[1, 14].Value = "Bỏ qua";
                worksheet.Cells[1, 15].Value = "Tổng điểm";
                worksheet.Cells[1, 16].Value = "Điểm tối đa";

                int row = 2;
                int stt = 1;
                foreach (var item in result)
                {
                    worksheet.Cells[row, 1].Value = stt++;
                    worksheet.Cells[row, 2].Value = item.MaDeThi;
                    worksheet.Cells[row, 3].Value = item.TenDeThi;
                    worksheet.Cells[row, 4].Value = item.MoTa;
                    worksheet.Cells[row, 5].Value = item.MaNhanVien;
                    worksheet.Cells[row, 6].Value = item.FullName;
                    worksheet.Cells[row, 7].Value = item.Email;
                    worksheet.Cells[row, 8].Value = item.ThoiGianBatDau;
                    worksheet.Cells[row, 9].Value = item.ThoiGianKetThuc;
                    worksheet.Cells[row, 10].Value = item.Duration;
                    worksheet.Cells[row, 11].Value = item.TotalQuestions;
                    worksheet.Cells[row, 12].Value = item.TotalCorrect;
                    worksheet.Cells[row, 13].Value = item.TotalWrong;
                    worksheet.Cells[row, 14].Value = item.TotalUnanswered;
                    worksheet.Cells[row, 15].Value = item.TotalScore;
                    worksheet.Cells[row, 16].Value = item.MaxScore;

                    row++;
                }
                worksheet.Cells[1, 1, row - 1, 17].AutoFitColumns();
                var fileName = $"Bai-thi-da-nop( {deThi?.MaDeThi} - {deThi?.TenDeThi}).xlsx";
                return Ok(new { result, dataExcel = package.GetAsByteArray(), fileName });
            };

            return Ok(result);
        }

        // POST: api/EES/BaiThi/Start
        /// <summary>
        /// Tạo mới DeThi cùng các PhanThi, CauHoi và LuaChon
        /// </summary>
        [HttpPost("Start")]
        public async Task<IActionResult> CreateBaiThi([FromBody] CreateBaiThiRequest request)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var baiThiId = await _examService.CreateBaiThiAsync(request);
                return CreatedAtAction(nameof(GetById), new { id = baiThiId }, new { id = baiThiId });
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
        }

        // POST: api/EES/BaiThi/{baiThiId}/start
        /// <summary>
        /// Lấy ra thông tin bài thi cùng câu hỏi và lựa chọn để thi
        /// </summary>
        [HttpGet("{baiThiId}/start")]
        public async Task<IActionResult> GetBaiThiToStart(Guid baiThiId, Guid userId)
        {
            var baiThi = await uow.BaiThis.GetSingleAsync(
                d => d.Id == baiThiId && !d.IsDeleted,
                includes: new[]
                {
                    "DeThi",
                    "PhanThiBaiThis.CauHoiPhanThiBaiThis.CauHoi.LuaChons"
                }
            );

            if (baiThi == null || baiThi.UserId != userId || baiThi.ThoiGianKetThuc != null)
            {
                return NotFound();
            }

            // Sử dụng lại CreateExamDto làm response
            var dto = new BaiThiDto
            {
                Id = baiThiId,
                DeThiId = baiThi.DeThiId,
                UserId = baiThi.UserId,
                Duration = baiThi.DeThi.Duration,
                ThoiGianBatDau = baiThi.ThoiGianBatDau,
                ThoiGianKetThuc = baiThi.ThoiGianKetThuc,
                MaxScore = baiThi.MaxScore,
                PhanThiBaiThis = baiThi.PhanThiBaiThis
                    .Where(pt => !pt.IsDeleted)
                    .OrderBy(pt => pt.ThuTu)
                    .Select(pt => new PhanThiBaiThiDto
                    {
                        Id = pt.Id,
                        TenPhanThiBaiThi = pt.TenPhanThiBaiThi,
                        ThuTu = pt.ThuTu,
                        CauHoiPhanThiBaiThis = pt.CauHoiPhanThiBaiThis
                            .Where(chptbt => !chptbt.IsDeleted)
                            .OrderBy(chptbt => chptbt.ThuTu)
                            .Select(chptbt =>
                            {
                                return new CauHoiPhanThiBaiThiDto
                                {
                                    Id = chptbt.Id,
                                    CauHoiId = chptbt.CauHoiId,
                                    ThuTu = chptbt.ThuTu,
                                    DiemPhanBo = (int)chptbt.DiemPhanBo,
                                    NoiDung = chptbt.CauHoi.NoiDung,
                                    LoaiCauHoi = chptbt.CauHoi.LoaiCauHoi,
                                    HinhAnhUrl = chptbt.CauHoi.HinhAnhUrl,
                                    AmThanhUrl = chptbt.CauHoi.AmThanhUrl,
                                    LuaChons = GetLuaChonsForCauHoi(chptbt)
                                        .Select(lc => new LuaChonBaiThiDto
                                        {
                                            Id = lc.Id,
                                            NoiDung = lc.NoiDung,
                                            ThuTu = lc.ThuTu
                                        })
                                        .ToList()
                                };
                            })
                            .ToList()
                    })
                    .ToList()
            };


            return Ok(dto);
        }

        /// <summary>
        ///  Lấy danh sách lựa chọn cho câu hỏi, tùy theo loại câu hỏi mà lọc hoặc sắp xếp
        /// </summary>
        /// <param name="chptbt"></param>
        /// <returns>Danh sách lựa chọn</returns>
        private static IEnumerable<LuaChon> GetLuaChonsForCauHoi(CauHoiPhanThiBaiThi chptbt)
        {
            var isOrdering = chptbt.CauHoi.LoaiCauHoi == "ordering";
            var isReading = chptbt.CauHoi.LoaiCauHoi == "true_false";
            // Lấy danh sách LuaChons đã include
            var allLuaChons = chptbt.CauHoi.LuaChons
                .Where(lc => !lc.IsDeleted)
                .AsEnumerable();  // chuyển sang LINQ-to-Objects

            IEnumerable<LuaChon> filteredLuaChons;

            if (isReading && !string.IsNullOrEmpty(chptbt.MenhDeIds))
            {
                // Nếu là câu hỏi reading comprehension, lọc theo mệnh đề
                var luaChonIds = JsonConvert.DeserializeObject<List<Guid>>(chptbt.MenhDeIds);
                filteredLuaChons = allLuaChons.Where(lc => luaChonIds.Contains(lc.Id));
            }
            else if (isOrdering)
            {
                // Nếu là câu hỏi sắp xếp, đảo ngẫu nhiên thứ tự
                filteredLuaChons = allLuaChons.OrderBy(_ => Guid.NewGuid());
            }
            else
            {
                // Nếu là câu hỏi lựa chọn thông thường, lấy theo thứ tự
                filteredLuaChons = allLuaChons.OrderBy(lc => lc.ThuTu);
            }

            return filteredLuaChons;
        }



        [HttpPost("{id}/finish")]
        public async Task<IActionResult> NopBaiThi(Guid id, [FromForm] SubmitExamRequest req)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            if (id != req.BaiThiId)
            {
                return BadRequest("ID không khớp với dữ liệu gửi lên");
            }

            try
            {
                await _examService.NopBaiThiAsync(req);
                return Ok(new { Message = "Nộp bài thành công" });
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
        }


        /// <summary>
        /// Lấy chi tiết bài thi đã nộp, bao gồm câu hỏi, lựa chọn và câu trả lời
        /// </summary>
        /// <param name="baiThiId"></param>
        /// <returns></returns>
        [HttpGet("{baiThiId}/detail")]
        public async Task<IActionResult> GetDetail(Guid baiThiId)
        {
            var baiThiDaNop = await uow.BaiThis.GetSingleAsync(bt => !bt.IsDeleted && bt.Id == baiThiId,
                    includes: new[] {
                        "DeThi",
                        "PhanThiBaiThis.CauHoiPhanThiBaiThis.CauHoi.LuaChons",
                        "PhanThiBaiThis.CauHoiPhanThiBaiThis.CauTraLois"
                    }
                );

            if (baiThiDaNop is null) return NotFound();

            // Tính tổng, phân nhóm theo Part
            var phanThis = baiThiDaNop.PhanThiBaiThis
                .OrderBy(pt => pt.ThuTu)
                .Select(pt => new PartDto
                {
                    Id = pt.Id,
                    TenPhanThi = $"Part {pt.PhanThi}",
                    CauHois = pt.CauHoiPhanThiBaiThis
                        .Select((chpt, idx) =>
                        {
                            var cauTraLoi = chpt.CauTraLois.FirstOrDefault();
                            var cauHoi = chpt.CauHoi;

                            // Map id → kí tự A/B/C hoặc chính nội dung (ordering)
                            var letterMap = cauHoi.LuaChons
                                .Where(lc => !lc.IsDeleted)
                                .ToDictionary(
                                    lc => lc.Id.ToString(),
                                    lc =>
                                    {
                                        var isOrdering = cauHoi.LoaiCauHoi.Contains("ordering");
                                        return isOrdering ? lc.NoiDung : ((char)('A' + lc.ThuTu - 1)).ToString();
                                    }
                                );

                            //string ToJson(string s) => string.IsNullOrWhiteSpace(s) ? "[]" : s;

                            //List<string> ParseIds(string raw)
                            //{
                            //    var token = JToken.Parse(ToJson(raw));
                            //    return token.Type == JTokenType.Array
                            //        ? token.ToObject<List<string>>()
                            //        : new List<string> { token.ToObject<string>() };
                            //}

                            List<string> ParseIds(string raw)
                            {
                                if (string.IsNullOrWhiteSpace(raw)) return new();
                                var token = JToken.Parse(raw);
                                return token.Type == JTokenType.Array
                                    ? token.ToObject<List<string>>()
                                    : new List<string> { token.ToObject<string>() };
                            }

                            var selIds = ParseIds(cauTraLoi?.SelectedLuaChonIds);
                            var ordIds = ParseIds(cauTraLoi?.OrderingAnswer);

                            //var userAnswer = string.Empty;
                            //if (cauHoi.LoaiCauHoi.Contains("choice"))
                            //{
                            //    userAnswer = string.Join(", ",
                            //        selIds?.Select(id =>
                            //            letterMap.TryGetValue(id, out var letter) ? letter : "?"
                            //        ) ?? new List<string>()
                            //    );
                            //}
                            //else if (cauHoi.LoaiCauHoi == "short_answer")
                            //{
                            //    userAnswer = cauTraLoi?.ShortAnswerText;
                            //}
                            //else if (cauHoi.LoaiCauHoi == "ordering")
                            //{
                            //    userAnswer = string.Join(", ",
                            //        ordIds?.Select(id =>
                            //            letterMap.TryGetValue(id, out var letter) ? letter : "?"
                            //        ) ?? new List<string>()
                            //    );
                            //}
                            //else if (cauHoi.LoaiCauHoi == "voice_record")
                            //{
                            //    userAnswer = string.IsNullOrWhiteSpace(cauTraLoi?.AudioText) ? null : $"♫ {cauTraLoi.AudioText} ♫";
                            //}

                            var userAnswer = string.Empty;
                            switch (cauHoi.LoaiCauHoi)
                            {
                                case var t when t.Contains("choice"):
                                    userAnswer = string.Join(", ", selIds.Select(id => letterMap.GetValueOrDefault(id, "?")));
                                    break;

                                case "short_answer":
                                    userAnswer = cauTraLoi?.ShortAnswerText;
                                    break;

                                case "ordering":
                                    userAnswer = string.Join(" ", ordIds.Select(id => letterMap.GetValueOrDefault(id, "?")));
                                    break;

                                case "voice_record":
                                    userAnswer = string.IsNullOrWhiteSpace(cauTraLoi?.AudioText) ? null : $"♫ {cauTraLoi.AudioText} ♫";
                                    break;

                                case "true_false":
                                    //Mệnh đề được random
                                    var menhDeIds = JsonConvert.DeserializeObject<List<Guid>>(chpt.MenhDeIds ?? "[]") ?? new();

                                    // Câu tra lời thí sinh
                                    var tfDict = JsonConvert.DeserializeObject<Dictionary<Guid, bool>>(
                                                     cauTraLoi?.BooleanAnswersJson ?? "{}") ?? new();

                                    // Hiển thị: T / F / - (bỏ chọn) cho mỗi mệnh đề thí sinh
                                    var arr = menhDeIds.Select(id =>
                                            tfDict.TryGetValue(id, out var pick)
                                                ? (pick ? "T" : "F")
                                                : "-");

                                    userAnswer = string.Join(", ", arr);
                                    break;

                                default:
                                    userAnswer = null;
                                    break;
                            }

                            //var correctAnswer = string.Empty;

                            //if (cauHoi.LoaiCauHoi.Contains("choice"))
                            //{
                            //    correctAnswer = string.Join(", ",
                            //        cauHoi.LuaChons
                            //        .Where(lc => !lc.IsDeleted && lc.IsCorrect)
                            //        .Select(lc => ((char)('A' + lc.ThuTu - 1)).ToString())
                            //    );
                            //}
                            //else if (cauHoi.LoaiCauHoi.Contains("ordering"))
                            //{
                            //    correctAnswer = string.Join(", ",
                            //            cauHoi.LuaChons
                            //        .Where(lc => !lc.IsDeleted)
                            //        .OrderBy(lc => lc.ThuTu)
                            //        .Select(lc => lc.NoiDung)
                            //    );
                            //}
                            //else if (cauHoi.LoaiCauHoi == "voice_record" || cauHoi.LoaiCauHoi == "short_answer")
                            //{
                            //    correctAnswer = cauHoi.DapAnMau;
                            //}
                            //else
                            //{
                            //    correctAnswer = null;
                            //}
                            string correctAnswer = string.Empty;
                            switch (cauHoi.LoaiCauHoi)
                            {
                                case var t when t.Contains("choice"):
                                    correctAnswer = string.Join(", ", cauHoi.LuaChons.Where(l => l.IsCorrect && !l.IsDeleted).Select(l => ((char)('A' + l.ThuTu - 1)).ToString()));
                                    break;

                                case var t when t.Contains("ordering"):
                                    correctAnswer = string.Join(" ", cauHoi.LuaChons.Where(l => !l.IsDeleted).OrderBy(l => l.ThuTu).Select(l => l.NoiDung));
                                    break;

                                case "true_false":
                                    //Mệnh đề được random
                                    var menhDeIds = JsonConvert.DeserializeObject<List<Guid>>(chpt.MenhDeIds ?? "[]") ?? new();

                                    // Lấy thông tin từ LuaChons
                                    var map = cauHoi.LuaChons
                                        .Where(lc => !lc.IsDeleted)
                                        .ToDictionary(lc => lc.Id, lc => lc.IsCorrect);

                                    // Hiển thị đáp án T / F cho mỗi lựa chọn
                                    var arr = menhDeIds.Select(id =>
                                        map.TryGetValue(id, out var correct)
                                            ? (correct ? "T" : "F")
                                            : "-");

                                    correctAnswer = string.Join(", ", arr);
                                    break;


                                case "voice_record":
                                case "short_answer":
                                    correctAnswer = cauHoi.DapAnMau ?? string.Empty;
                                    break;

                                default:
                                    correctAnswer = string.Empty;
                                    break;

                            }
                            //string? correctAnswer = cauHoi.LoaiCauHoi switch
                            //{
                            //    var t when t.Contains("choice") =>
                            //        string.Join(", ",
                            //            cauHoi.LuaChons.Where(l => l.IsCorrect && !l.IsDeleted)
                            //                           .Select(l => ((char)('A' + l.ThuTu - 1)).ToString())),
                            //    var t when t.Contains("ordering") =>
                            //        string.Join(", ",
                            //            cauHoi.LuaChons.Where(l => !l.IsDeleted)
                            //                           .OrderBy(l => l.ThuTu)
                            //                           .Select(l => l.NoiDung)),
                            //    "voice_record" or "short_answer" => cauHoi.DapAnMau,
                            //    _ => null
                            //};

                            return new QuestionDetailDto
                            {
                                Id = chpt.CauHoiId,
                                CauHoiPhanThiBaiThiId = chpt.Id,
                                Number = chpt.ThuTu,
                                LoaiCauHoi = chpt.CauHoi.LoaiCauHoi,
                                UserAnswer = userAnswer,
                                CorrectAnswer = correctAnswer,
                                IsCorrect = chpt.CauTraLois == null
                                    ? (bool?)null
                                    : chpt.CauTraLois.FirstOrDefault(ctl => !ctl.IsDeleted)?.IsCorrect,
                                Score = chpt.CauTraLois == null
                                    ? 0
                                    : chpt.CauTraLois.FirstOrDefault(ctl => !ctl.IsDeleted)?.Diem,
                            };

                        })
                        .OrderBy(ch => ch.Number)
                        .ToList()
                })
                .ToList();

            var user = await _userService.GetByIdAsync(baiThiDaNop.UserId ?? Guid.Empty);
            // Tính tổng điểm   
            var dto = new BaiThiDaNopDto
            {
                Id = baiThiDaNop.Id,
                TenThiSinh = user?.FullName ?? string.Empty,
                MaThiSinh = user?.MaNhanVien ?? string.Empty,
                MaDeThi = baiThiDaNop.DeThi.MaDeThi,
                tenDeThi = baiThiDaNop.DeThi.TenDeThi,
                ThoiGianBatDau = baiThiDaNop.ThoiGianBatDau,
                Duration = ((TimeSpan?)(baiThiDaNop.ThoiGianKetThuc - baiThiDaNop.ThoiGianBatDau))?.ToString(@"hh\:mm\:ss") ?? string.Empty,
                TotalQuestions = phanThis.Sum(p => p.CauHois.Count),
                CorrectCount = phanThis.Sum(p => p.CauHois.Count(q => q.IsCorrect == true)),
                WrongCount = phanThis.Sum(p => p.CauHois.Count(q => q.IsCorrect == false)),
                SkippedCount = phanThis.Sum(p => p.CauHois.Count(q => q.IsCorrect == null)),
                TotalScore = baiThiDaNop.TotalScore,
                MaxScore = baiThiDaNop.MaxScore,
                PhanThis = phanThis
            };

            return Ok(dto);
        }

        [HttpGet("{baiThiId}/cau-hoi-instance/{cauHoiPhanThiBaiThiId}/cau-tra-loi")]
        public async Task<IActionResult> GetCauTraLoiForCauHoi(Guid baiThiId, Guid cauHoiPhanThiBaiThiId)
        {
            var cauTraLoi = await uow.CauTraLois.GetSingleAsync(
                    ctl => !ctl.IsDeleted
                    && ctl.CauHoiPhanThiBaiThi.Id == cauHoiPhanThiBaiThiId
                    && ctl.CauHoiPhanThiBaiThi.PhanThiBaiThi.BaiThiId == baiThiId,
                includes: new[] { "CauHoiPhanThiBaiThi.CauHoi.LuaChons" }
            );

            if (cauTraLoi == null)
            {
                return Conflict("Thí sinh chưa trả lời");
            }

            var dto = new CauTraLoiDto
            {
                Id = cauTraLoi.Id,
                CauHoiPhanThiBaiThiId = cauTraLoi.CauHoiPhanThiBaiThiId,
                SelectedLuaChonIds = cauTraLoi.SelectedLuaChonIds,
                ShortAnswerText = cauTraLoi.ShortAnswerText,
                OrderingAnswer = cauTraLoi.OrderingAnswer,
                BooleanAnswersJson = cauTraLoi.BooleanAnswersJson,
                AudioUrl = cauTraLoi.AudioUrl,
                AudioText = cauTraLoi.AudioText,
                IsCorrect = cauTraLoi.IsCorrect,
                Diem = cauTraLoi.Diem,
                ThoiGian = cauTraLoi.ThoiGian
            };

            return Ok(dto);
        }

        public class CauHoiVaTraLoiDto
        {
            // Thông tin câu hỏi
            public Guid CauHoiId { get; set; }
            public string NoiDung { get; set; }
            public string LoaiCauHoi { get; set; }
            public string? HinhAnhUrl { get; set; }
            public string? AmThanhUrl { get; set; }
            public string? DapAnMau { get; set; }
            public IEnumerable<TagDto>? Tags { get; set; }
            public IEnumerable<LuaChonDto>? LuaChons { get; set; }
            public string? MenhDeIds { get; set; }  // Chỉ dùng cho câu hỏi true_false

            // Thông tin câu trả lời (null nếu chưa nộp)
            public CauTraLoiDto? CauTraLoi { get; set; }
        }

        public record TagDto(Guid Id, string TenTag);
        public record LuaChonDto(Guid Id, string NoiDung, int ThuTu, bool? IsCorrect);

        [HttpGet("{baiThiId}/cau-hoi-instance/{cauHoiPhanThiBaiThiId}")]
        public async Task<IActionResult> GetQuestionAndAnswer(Guid baiThiId, Guid cauHoiPhanThiBaiThiId)
        {
            // 1. Lấy bản ghi instance + quan hệ
            var chptbt = await uow.CauHoiPhanThiBaiThis.GetSingleAsync(
                ch => !ch.IsDeleted && ch.Id == cauHoiPhanThiBaiThiId
                    && ch.PhanThiBaiThi.BaiThiId == baiThiId,
                includes: new[]
                {
                    "CauHoi.LuaChons",
                    "CauHoi.CauHoiTags.Tag",
                    "CauTraLois"          // (một bản ghi duy nhất/người)
                });

            if (chptbt is null)
                return NotFound();

            var ch = chptbt.CauHoi;
            var cauTraLoi = chptbt.CauTraLois
                .FirstOrDefault(ctl => !ctl.IsDeleted);      // có thể null

            // 2. Build DTO
            var dto = new CauHoiVaTraLoiDto
            {
                CauHoiId = ch.Id,
                NoiDung = ch.NoiDung,
                LoaiCauHoi = ch.LoaiCauHoi,
                HinhAnhUrl = ch.HinhAnhUrl,
                AmThanhUrl = ch.AmThanhUrl,
                DapAnMau = ch.DapAnMau,
                Tags = ch.CauHoiTags
                        .Where(t => !t.IsDeleted)
                        .Select(t => new TagDto(t.Tag.Id, t.Tag.TenTag)),
                LuaChons = ch.LuaChons
                        .Where(l => !l.IsDeleted)
                        .OrderBy(l => l.ThuTu)
                        .Select(l => new LuaChonDto(l.Id, l.NoiDung, l.ThuTu, l.IsCorrect)),
                MenhDeIds = chptbt.MenhDeIds,
                CauTraLoi = cauTraLoi == null ? null : new CauTraLoiDto
                {
                    Id = cauTraLoi.Id,
                    CauHoiPhanThiBaiThiId = cauTraLoi.CauHoiPhanThiBaiThiId,
                    SelectedLuaChonIds = cauTraLoi.SelectedLuaChonIds,
                    ShortAnswerText = cauTraLoi.ShortAnswerText,
                    OrderingAnswer = cauTraLoi.OrderingAnswer,
                    BooleanAnswersJson = cauTraLoi.BooleanAnswersJson,
                    AudioUrl = cauTraLoi.AudioUrl,
                    AudioText = cauTraLoi.AudioText,
                    IsCorrect = cauTraLoi.IsCorrect,
                    Diem = cauTraLoi.Diem,
                    ThoiGian = cauTraLoi.ThoiGian
                }
            };

            return Ok(dto);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var baithi = await uow.BaiThis.GetSingleAsync(bt => bt.Id == id);
            if(baithi == null)
            {
                return NotFound();
            }

            baithi.IsDeleted = true;
            baithi.DeletedDate = DateTime.Now;
            await uow.CompleteAsync();

            return NoContent();
        }
    }
}
