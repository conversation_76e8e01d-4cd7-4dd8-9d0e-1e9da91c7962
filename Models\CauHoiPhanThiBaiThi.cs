﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace eExamSmart_BE.Models
{
    public class CauHoiPhanThiBaiThi : Auditable
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public Guid PhanThiBaiThiId { get; set; }
        [ForeignKey(nameof(PhanThiBaiThiId))]
        public PhanThiBaiThi PhanThiBaiThi { get; set; }

        public Guid CauHoiId { get; set; }
        [ForeignKey(nameof(CauHoiId))]
        public CauHoi CauHoi { get; set; }

        public float DiemPhanBo { get; set; }

        public int ThuTu { get; set; }

        public string? MenhDeIds { get; set; } // Chứa các mệnh đề nếu là loại reading comprehension


        [MaxLength(20)]
        public string Nguon { get; set; } = string.Empty;

        public ICollection<CauTraLoi> CauTraLois { get; set; } = new List<CauTraLoi>();
    }
}
