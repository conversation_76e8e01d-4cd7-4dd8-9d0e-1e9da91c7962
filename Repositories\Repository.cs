﻿using eExamSmart_BE.Data;
using eExamSmart_BE.Infrastructure;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace eExamSmart_BE.Repositories
{
    public class Repository<T> : IRepository<T> where T : class
    {
        protected readonly MyDbContext _db;
        private readonly DbSet<T> _dbSet;
        protected Repository(MyDbContext db)
        {
            _db = db;
            _dbSet = db.Set<T>();
        }
        public async Task<T> AddAsync(T entity)
        {
            // EF Core cung cấp AddAsync chủ yếu khi làm việc với các DB được remote (VD: CosmosDB)
            var entry = await _dbSet.AddAsync(entity);
            return entry.Entity;
        }

        public async Task AddRangeAsync(IEnumerable<T> entities)
        {
            await _dbSet.AddRangeAsync(entities);
        }

        public Task UpdateAsync(T entity)
        {
            // Cập nhật được thực hiện đồng bộ
            _dbSet.Attach(entity);
            _db.Entry(entity).State = EntityState.Modified;
            return Task.CompletedTask;
        }

        public Task UpdateRangeAsync(IEnumerable<T> entities)
        {
            foreach (var entity in entities)
            {
                _dbSet.Attach(entity);
                _db.Entry(entity).State = EntityState.Modified;
            }
            return Task.CompletedTask;
        }

        public async Task DeleteAsync(object id)
        {
            var entity = await _dbSet.FindAsync(id);
            if (entity != null)
            {
                Delete(entity);
            }
        }

        // Phương thức Delete thực tế (không public vì chỉ dùng nội bộ)
        protected virtual void Delete(T entity)
        {
            _dbSet.Attach(entity);
            _dbSet.Remove(entity);
        }

        public async Task DeleteRangeAsync(List<object> ids)
        {
            foreach (var id in ids)
            {
                var entity = await _dbSet.FindAsync(id);
                if (entity != null)
                {
                    Delete(entity);
                }
            }
        }

        public Task RemoveRangeAsync(IEnumerable<T> entities)
        {
            foreach (var entity in entities)
            {
                _dbSet.Attach(entity);
                _dbSet.Remove(entity);
            }
            return Task.CompletedTask;
        }

        public async Task<T?> GetByIdAsync(object id)
        {
            return await _dbSet.FindAsync(id);
        }

        public async Task<T?> GetSingleAsync(Expression<Func<T, bool>> whereCondition, string[]? includes = null)
        {
            IQueryable<T> query = _dbSet;
            if (includes != null && includes.Any())
            {
                // Xử lý includes
                query = query.Include(includes.First());
                foreach (var include in includes.Skip(1))
                    query = query.Include(include);
            }
            return await query.Where(whereCondition).FirstOrDefaultAsync();
        }

        public async Task<ICollection<T>> GetAll_No_ConditionAsync(Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null, string[]? includes = null)
        {
            IQueryable<T> query = _dbSet;
            if (includes != null && includes.Any())
            {
                query = query.Include(includes.First());
                foreach (var include in includes.Skip(1))
                    query = query.Include(include);
            }
            if (orderBy != null)
            {
                query = orderBy(query);
            }
            return await query.ToListAsync();
        }

        public async Task<ICollection<T>> GetAllAsync(Expression<Func<T, bool>> whereCondition, Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null, string[]? includes = null)
        {
            IQueryable<T> query = _dbSet.Where(whereCondition);
            if (includes != null && includes.Any())
            {
                query = query.Include(includes.First());
                foreach (var include in includes.Skip(1))
                    query = query.Include(include);
            }
            if (orderBy != null)
            {
                query = orderBy(query);
            }
            return await query.ToListAsync();
        }

        public async Task<(ICollection<T> Data, int Total)> GetAllPagingAsync(
            Expression<Func<T, bool>> whereCondition,
            int page,
            int pageSize,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
            string[]? includes = null)
        {
            IQueryable<T> query = _dbSet.Where(whereCondition);
            if (includes != null && includes.Any())
            {
                query = query.Include(includes.First());
                foreach (var include in includes.Skip(1))
                    query = query.Include(include);
            }
            if (orderBy != null)
            {
                query = orderBy(query);
            }
            int total = await query.CountAsync();
            var data = await query.Skip((page - 1) * pageSize)
                                  .Take(pageSize)
                                  .ToListAsync();
            return (data, total);
        }

        public async Task<(ICollection<T> Data, int TotalRow, int TotalPage)> GetAllPaging1Async(
            int page,
            int pageSize,
            Expression<Func<T, bool>>? whereCondition = null,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
            string[]? includes = null)
        {
            IQueryable<T> query = _dbSet;
            if (whereCondition != null)
            {
                query = query.Where(whereCondition);
            }
            if (includes != null && includes.Any())
            {
                query = includes.Aggregate(query, (current, include) => current.Include(include));
            }
            if (orderBy != null)
            {
                query = orderBy(query);
            }
            int totalRow = await query.CountAsync();
            int totalPage = (int)Math.Ceiling(totalRow / (double)pageSize);
            var data = await query.Skip((page - 1) * pageSize)
                                  .Take(pageSize)
                                  .AsNoTracking()
                                  .ToListAsync();
            return (data, totalRow, totalPage);
        }

        public async Task<int> CountAsync(Expression<Func<T, bool>> whereCondition)
        {
            return await _dbSet.CountAsync(whereCondition);
        }

        public async Task<bool> ExistsAsync(Expression<Func<T, bool>> whereCondition)
        {
            // Sử dụng AnyAsync sẽ tối ưu hơn so với CountAsync > 0
            return await _dbSet.AnyAsync(whereCondition);
        }

        public async Task<ICollection<T>> ExecWithStoreProcedureAsync(string query, params object[] parameters)
        {
            return await _dbSet.FromSqlRaw(query, parameters).ToListAsync();
        }

        public async Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> whereCondition,
                                                   Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
                                                   string[]? includes = null)
        {
            IQueryable<T> query = _dbSet;
            if (includes != null && includes.Any())
            {
                query = includes.Aggregate(query, (current, include) => current.Include(include));
            }
            query = query.Where(whereCondition);
            if (orderBy != null)
            {
                query = orderBy(query);
            }
            return await query.FirstOrDefaultAsync();
        }

        public IQueryable<T> Query(Expression<Func<T, bool>>? filter = null, string[]? includes = null)
        {
            IQueryable<T> query = _dbSet;

            if (filter != null)
                query = query.Where(filter);

            if (includes != null && includes.Any())
                query = includes.Aggregate(query, (q, inc) => q.Include(inc));

            return query.AsQueryable();   // không thực thi
        }
    }
}
